/* 
 * DateJS Culture String File
 * Country Code: lt-LT
 * Name: Lithuanian (Lithuania)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["lt-LT"] = {
        "name": "lt-LT",
        "englishName": "Lithuanian (Lithuania)",
        "nativeName": "lietuvių (Lietuva)",
        "Sunday": "sekmadienis",
        "Monday": "pirmadienis",
        "Tuesday": "antradienis",
        "Wednesday": "trečiadienis",
        "Thursday": "ketvirtadienis",
        "Friday": "penktadienis",
        "Saturday": "šeštadienis",
        "Sun": "Sk",
        "Mon": "Pr",
        "Tue": "An",
        "Wed": "Tr",
        "Thu": "Kt",
        "Fri": "Pn",
        "Sat": "Št",
        "Su": "S",
        "Mo": "P",
        "Tu": "A",
        "We": "T",
        "Th": "K",
        "Fr": "Pn",
        "Sa": "Š",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "P",
        "T_Tue_Initial": "A",
        "W_Wed_Initial": "T",
        "T_Thu_Initial": "K",
        "F_Fri_Initial": "P",
        "S_Sat_Initial": "Š",
        "January": "sausis",
        "February": "vasaris",
        "March": "kovas",
        "April": "balandis",
        "May": "gegužė",
        "June": "birželis",
        "July": "liepa",
        "August": "rugpjūtis",
        "September": "rugsėjis",
        "October": "spalis",
        "November": "lapkritis",
        "December": "gruodis",
        "Jan_Abbr": "Sau",
        "Feb_Abbr": "Vas",
        "Mar_Abbr": "Kov",
        "Apr_Abbr": "Bal",
        "May_Abbr": "Geg",
        "Jun_Abbr": "Bir",
        "Jul_Abbr": "Lie",
        "Aug_Abbr": "Rgp",
        "Sep_Abbr": "Rgs",
        "Oct_Abbr": "Spl",
        "Nov_Abbr": "Lap",
        "Dec_Abbr": "Grd",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy.MM.dd",
        "dddd, MMMM dd, yyyy": "yyyy 'm.' MMMM d 'd.'",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy 'm.' MMMM d 'd.' HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM d 'd.'",
        "MMMM, yyyy": "yyyy 'm.' MMMM",
        "/jan(uary)?/": "sau(sis)?",
        "/feb(ruary)?/": "vas(aris)?",
        "/mar(ch)?/": "kov(as)?",
        "/apr(il)?/": "bal(andis)?",
        "/may/": "geg(užė)?",
        "/jun(e)?/": "bir(želis)?",
        "/jul(y)?/": "lie(pa)?",
        "/aug(ust)?/": "rugpjūtis",
        "/sep(t(ember)?)?/": "rugsėjis",
        "/oct(ober)?/": "spalis",
        "/nov(ember)?/": "lap(kritis)?",
        "/dec(ember)?/": "gruodis",
        "/^su(n(day)?)?/": "^s(k(kmadienis)?)?",
        "/^mo(n(day)?)?/": "^p(r(rmadienis)?)?",
        "/^tu(e(s(day)?)?)?/": "^a(n(tradienis)?)?",
        "/^we(d(nesday)?)?/": "^t(r(ečiadienis)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^k(t(tvirtadienis)?)?",
        "/^fr(i(day)?)?/": "^penktadienis",
        "/^sa(t(urday)?)?/": "^š(t(štadienis)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "lt-LT";
