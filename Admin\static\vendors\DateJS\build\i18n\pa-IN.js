/* 
 * DateJS Culture String File
 * Country Code: pa-IN
 * Name: Punjabi (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["pa-IN"] = {
        "name": "pa-IN",
        "englishName": "Punjabi (India)",
        "nativeName": "ਪੰਜਾਬੀ (ਭਾਰਤ)",
        "Sunday": "ਐਤਵਾਰ",
        "Monday": "ਸੋਮਵਾਰ",
        "Tuesday": "ਮੰਗਲਵਾਰ",
        "Wednesday": "ਬੁਧਵਾਰ",
        "Thursday": "ਵੀਰਵਾਰ",
        "Friday": "ਸ਼ੁੱਕਰਵਾਰ",
        "Saturday": "ਸ਼ਨੀਚਰਵਾਰ",
        "Sun": "ਐਤ.",
        "Mon": "ਸੋਮ.",
        "Tue": "ਮੰਗਲ.",
        "Wed": "ਬੁਧ.",
        "Thu": "ਵੀਰ.",
        "Fri": "ਸ਼ੁਕਰ.",
        "Sat": "ਸ਼ਨੀ.",
        "Su": "ਐ",
        "Mo": "ਸ",
        "Tu": "ਮ",
        "We": "ਬ",
        "Th": "ਵ",
        "Fr": "ਸ਼",
        "Sa": "ਸ਼",
        "S_Sun_Initial": "ਐ",
        "M_Mon_Initial": "ਸ",
        "T_Tue_Initial": "ਮ",
        "W_Wed_Initial": "ਬ",
        "T_Thu_Initial": "ਵ",
        "F_Fri_Initial": "ਸ਼",
        "S_Sat_Initial": "ਸ਼",
        "January": "ਜਨਵਰੀ",
        "February": "ਫ਼ਰਵਰੀ",
        "March": "ਮਾਰਚ",
        "April": "ਅਪ੍ਰੈਲ",
        "May": "ਮਈ",
        "June": "ਜੂਨ",
        "July": "ਜੁਲਾਈ",
        "August": "ਅਗਸਤ",
        "September": "ਸਤੰਬਰ",
        "October": "ਅਕਤੂਬਰ",
        "November": "ਨਵੰਬਰ",
        "December": "ਦਸੰਬਰ",
        "Jan_Abbr": "ਜਨਵਰੀ",
        "Feb_Abbr": "ਫ਼ਰਵਰੀ",
        "Mar_Abbr": "ਮਾਰਚ",
        "Apr_Abbr": "ਅਪ੍ਰੈਲ",
        "May_Abbr": "ਮਈ",
        "Jun_Abbr": "ਜੂਨ",
        "Jul_Abbr": "ਜੁਲਾਈ",
        "Aug_Abbr": "ਅਗਸਤ",
        "Sep_Abbr": "ਸਤੰਬਰ",
        "Oct_Abbr": "ਅਕਤੂਬਰ",
        "Nov_Abbr": "ਨਵੰਬਰ",
        "Dec_Abbr": "ਦਸੰਬਰ",
        "AM": "ਸਵੇਰੇ",
        "PM": "ਸ਼ਾਮ",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy dddd",
        "h:mm tt": "tt hh:mm",
        "h:mm:ss tt": "tt hh:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy dddd tt hh:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "ਜਨਵਰੀ",
        "/feb(ruary)?/": "ਫ਼ਰਵਰੀ",
        "/mar(ch)?/": "ਮਾਰਚ",
        "/apr(il)?/": "ਅਪ੍ਰੈਲ",
        "/may/": "ਮਈ",
        "/jun(e)?/": "ਜੂਨ",
        "/jul(y)?/": "ਜੁਲਾਈ",
        "/aug(ust)?/": "ਅਗਸਤ",
        "/sep(t(ember)?)?/": "ਸਤੰਬਰ",
        "/oct(ober)?/": "ਅਕਤੂਬਰ",
        "/nov(ember)?/": "ਨਵੰਬਰ",
        "/dec(ember)?/": "ਦਸੰਬਰ",
        "/^su(n(day)?)?/": "^ਐ(ਤ(.(ਵਾਰ)?)?)?",
        "/^mo(n(day)?)?/": "^ਸ(ੋਮ(.(ਵਾਰ)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^ਮ(ੰਗਲ(.(ਵਾਰ)?)?)?",
        "/^we(d(nesday)?)?/": "^ਬ(ੁਧ(.(ਵਾਰ)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ਵ(ੀਰ(.(ਵਾਰ)?)?)?",
        "/^fr(i(day)?)?/": "^ਸ਼(ੁਕਰ(.(ਰਵਾਰ)?)?)?",
        "/^sa(t(urday)?)?/": "^ਸ਼(ਨੀ(.(ਚਰਵਾਰ)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "pa-IN";
