/* 
 * DateJS Culture String File
 * Country Code: ro-RO
 * Name: Romanian (Romania)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ro-RO"] = {
        "name": "ro-RO",
        "englishName": "Romanian (Romania)",
        "nativeName": "română (România)",
        "Sunday": "duminică",
        "Monday": "luni",
        "Tuesday": "marţi",
        "Wednesday": "miercuri",
        "Thursday": "joi",
        "Friday": "vineri",
        "Saturday": "sâmbătă",
        "Sun": "D",
        "Mon": "L",
        "Tue": "Ma",
        "Wed": "Mi",
        "Thu": "J",
        "Fri": "V",
        "Sat": "S",
        "Su": "D",
        "Mo": "L",
        "Tu": "Ma",
        "We": "Mi",
        "Th": "J",
        "Fr": "V",
        "Sa": "S",
        "S_Sun_Initial": "D",
        "M_Mon_Initial": "L",
        "T_Tue_Initial": "M",
        "W_Wed_Initial": "M",
        "T_Thu_Initial": "J",
        "F_Fri_Initial": "V",
        "S_Sat_Initial": "S",
        "January": "ianuarie",
        "February": "februarie",
        "March": "martie",
        "April": "aprilie",
        "May": "mai",
        "June": "iunie",
        "July": "iulie",
        "August": "august",
        "September": "septembrie",
        "October": "octombrie",
        "November": "noiembrie",
        "December": "decembrie",
        "Jan_Abbr": "ian.",
        "Feb_Abbr": "feb.",
        "Mar_Abbr": "mar.",
        "Apr_Abbr": "apr.",
        "May_Abbr": "mai.",
        "Jun_Abbr": "iun.",
        "Jul_Abbr": "iul.",
        "Aug_Abbr": "aug.",
        "Sep_Abbr": "sep.",
        "Oct_Abbr": "oct.",
        "Nov_Abbr": "nov.",
        "Dec_Abbr": "dec.",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ian(.(uarie)?)?",
        "/feb(ruary)?/": "feb(.(ruarie)?)?",
        "/mar(ch)?/": "mar(.(tie)?)?",
        "/apr(il)?/": "apr(.(ilie)?)?",
        "/may/": "mai(.()?)?",
        "/jun(e)?/": "iun(.(ie)?)?",
        "/jul(y)?/": "iul(.(ie)?)?",
        "/aug(ust)?/": "aug(.(ust)?)?",
        "/sep(t(ember)?)?/": "sep(.(tembrie)?)?",
        "/oct(ober)?/": "oct(.(ombrie)?)?",
        "/nov(ember)?/": "noiembrie",
        "/dec(ember)?/": "dec(.(embrie)?)?",
        "/^su(n(day)?)?/": "^duminică",
        "/^mo(n(day)?)?/": "^luni",
        "/^tu(e(s(day)?)?)?/": "^marţi",
        "/^we(d(nesday)?)?/": "^miercuri",
        "/^th(u(r(s(day)?)?)?)?/": "^joi",
        "/^fr(i(day)?)?/": "^vineri",
        "/^sa(t(urday)?)?/": "^sâmbătă",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ro-RO";
