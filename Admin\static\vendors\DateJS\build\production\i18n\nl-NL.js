/* 
 * DateJS Culture String File
 * Country Code: nl-NL
 * Name: Dutch (Netherlands)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["nl-NL"] = {
        "name": "nl-NL",
        "englishName": "Dutch (Netherlands)",
        "nativeName": "Nederlands (Nederland)",
        "Sunday": "zondag",
        "Monday": "maandag",
        "Tuesday": "dinsdag",
        "Wednesday": "woensdag",
        "Thursday": "donderdag",
        "Friday": "vrijdag",
        "Saturday": "zaterdag",
        "Sun": "zo",
        "Mon": "ma",
        "Tue": "di",
        "Wed": "wo",
        "Thu": "do",
        "Fri": "vr",
        "Sat": "za",
        "Su": "zo",
        "Mo": "ma",
        "Tu": "di",
        "We": "wo",
        "Th": "do",
        "Fr": "vr",
        "Sa": "za",
        "S_Sun_Initial": "z",
        "M_Mon_Initial": "m",
        "T_Tue_Initial": "d",
        "W_Wed_Initial": "w",
        "T_Thu_Initial": "d",
        "F_Fri_Initial": "v",
        "S_Sat_Initial": "z",
        "January": "januari",
        "February": "februari",
        "March": "maart",
        "April": "april",
        "May": "mei",
        "June": "juni",
        "July": "juli",
        "August": "augustus",
        "September": "september",
        "October": "oktober",
        "November": "november",
        "December": "december",
        "Jan_Abbr": "jan",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mrt",
        "Apr_Abbr": "apr",
        "May_Abbr": "mei",
        "Jun_Abbr": "jun",
        "Jul_Abbr": "jul",
        "Aug_Abbr": "aug",
        "Sep_Abbr": "sep",
        "Oct_Abbr": "okt",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dec",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d-M-yyyy",
        "dddd, MMMM dd, yyyy": "dddd d MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd d MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(uari)?",
        "/feb(ruary)?/": "feb(ruari)?",
        "/mar(ch)?/": "maart",
        "/apr(il)?/": "apr(il)?",
        "/may/": "mei",
        "/jun(e)?/": "jun(i)?",
        "/jul(y)?/": "jul(i)?",
        "/aug(ust)?/": "aug(ustus)?",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "okt(ober)?",
        "/nov(ember)?/": "nov(ember)?",
        "/dec(ember)?/": "dec(ember)?",
        "/^su(n(day)?)?/": "^zondag",
        "/^mo(n(day)?)?/": "^maandag",
        "/^tu(e(s(day)?)?)?/": "^dinsdag",
        "/^we(d(nesday)?)?/": "^woensdag",
        "/^th(u(r(s(day)?)?)?)?/": "^donderdag",
        "/^fr(i(day)?)?/": "^vrijdag",
        "/^sa(t(urday)?)?/": "^zaterdag",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "nl-NL";
