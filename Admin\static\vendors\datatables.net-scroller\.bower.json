{"name": "datatables.net-scroller", "description": "Scroller for DataTables ", "main": ["js/dataTables.scroller.js"], "keywords": ["virtual scrolling", "DataTables", "j<PERSON><PERSON><PERSON>", "table", "DataTables"], "dependencies": {"jquery": ">=1.7", "datatables.net": ">=1.10.9"}, "moduleType": ["globals", "amd", "node"], "ignore": ["composer.json", "datatables.json", "package.json"], "authors": [{"name": "SpryMedia Ltd", "homepage": "https://datatables.net"}], "homepage": "https://datatables.net", "license": "MIT", "version": "1.4.2", "_release": "1.4.2", "_resolution": {"type": "version", "tag": "1.4.2", "commit": "8626dca4ad6777ed440e8cd9c46703d63a344ecf"}, "_source": "https://github.com/DataTables/Dist-DataTables-Scroller.git", "_target": "^1.4.1", "_originalSource": "datatables.net-scroller"}