/* 
 * DateJS Culture String File
 * Country Code: te-IN
 * Name: Telugu (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["te-IN"] = {
        "name": "te-IN",
        "englishName": "Telugu (India)",
        "nativeName": "తెలుగు (భారత దేశం)",
        "Sunday": "ఆదివారం",
        "Monday": "సోమవారం",
        "Tuesday": "మంగళవారం",
        "Wednesday": "బుధవారం",
        "Thursday": "గురువారం",
        "Friday": "శుక్రవారం",
        "Saturday": "శనివారం",
        "Sun": "ఆది.",
        "Mon": "సోమ.",
        "Tue": "మంగళ.",
        "Wed": "బుధ.",
        "Thu": "గురు.",
        "Fri": "శుక్ర.",
        "Sat": "శని.",
        "Su": "ఆ",
        "Mo": "స",
        "Tu": "మ",
        "We": "బ",
        "Th": "గ",
        "Fr": "శ",
        "Sa": "శ",
        "S_Sun_Initial": "ఆ",
        "M_Mon_Initial": "స",
        "T_Tue_Initial": "మ",
        "W_Wed_Initial": "బ",
        "T_Thu_Initial": "గ",
        "F_Fri_Initial": "శ",
        "S_Sat_Initial": "శ",
        "January": "జనవరి",
        "February": "ఫిబ్రవరి",
        "March": "మార్చి",
        "April": "ఏప్రిల్",
        "May": "మే",
        "June": "జూన్",
        "July": "జూలై",
        "August": "ఆగస్టు",
        "September": "సెప్టెంబర్",
        "October": "అక్టోబర్",
        "November": "నవంబర్",
        "December": "డిసెంబర్",
        "Jan_Abbr": "జనవరి",
        "Feb_Abbr": "ఫిబ్రవరి",
        "Mar_Abbr": "మార్చి",
        "Apr_Abbr": "ఏప్రిల్",
        "May_Abbr": "మే",
        "Jun_Abbr": "జూన్",
        "Jul_Abbr": "జూలై",
        "Aug_Abbr": "ఆగస్టు",
        "Sep_Abbr": "సెప్టెంబర్",
        "Oct_Abbr": "అక్టోబర్",
        "Nov_Abbr": "నవంబర్",
        "Dec_Abbr": "డిసెంబర్",
        "AM": "పూర్వాహ్న",
        "PM": "అపరాహ్న",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "జనవరి",
        "/feb(ruary)?/": "ఫిబ్రవరి",
        "/mar(ch)?/": "మార్చి",
        "/apr(il)?/": "ఏప్రిల్",
        "/may/": "మే",
        "/jun(e)?/": "జూన్",
        "/jul(y)?/": "జూలై",
        "/aug(ust)?/": "ఆగస్టు",
        "/sep(t(ember)?)?/": "సెప్టెంబర్",
        "/oct(ober)?/": "అక్టోబర్",
        "/nov(ember)?/": "నవంబర్",
        "/dec(ember)?/": "డిసెంబర్",
        "/^su(n(day)?)?/": "^ఆ(ది(.(వారం)?)?)?",
        "/^mo(n(day)?)?/": "^స(ోమ(.(వారం)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^మ(ంగళ(.(వారం)?)?)?",
        "/^we(d(nesday)?)?/": "^బ(ుధ(.(వారం)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^గ(ురు(.(వారం)?)?)?",
        "/^fr(i(day)?)?/": "^శ(ుక్ర(.(వారం)?)?)?",
        "/^sa(t(urday)?)?/": "^శ(ని(.(వారం)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "te-IN";
