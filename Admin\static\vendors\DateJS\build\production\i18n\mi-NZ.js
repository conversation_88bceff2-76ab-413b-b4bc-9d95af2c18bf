/* 
 * DateJS Culture String File
 * Country Code: mi-NZ
 * Name: <PERSON><PERSON> (New Zealand)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["mi-NZ"] = {
        "name": "mi-NZ",
        "englishName": "<PERSON><PERSON> (New Zealand)",
        "nativeName": "<PERSON>o <PERSON> (Aotearoa)",
        "Sunday": "Rā<PERSON><PERSON>",
        "Monday": "Mane",
        "Tuesday": "Tūrei",
        "Wednesday": "Wenerei",
        "Thursday": "Tāite",
        "Friday": "Paraire",
        "Saturday": "<PERSON><PERSON><PERSON><PERSON>",
        "Sun": "Ta",
        "Mon": "Ma",
        "Tue": "Tū",
        "Wed": "We",
        "Thu": "Tāi",
        "Fri": "Pa",
        "Sat": "Hā",
        "Su": "Ta",
        "Mo": "Ma",
        "Tu": "Tū",
        "We": "We",
        "Th": "<PERSON>ā<PERSON>",
        "Fr": "Pa",
        "Sa": "Hā",
        "S_Sun_Initial": "T",
        "M_Mon_Initial": "M",
        "T_Tue_Initial": "T",
        "W_Wed_Initial": "W",
        "T_Thu_Initial": "T",
        "F_Fri_Initial": "P",
        "S_Sat_Initial": "H",
        "January": "Kohi-tātea",
        "February": "Hui-tanguru",
        "March": "Poutū-te-rangi",
        "April": "Paenga-whāwhā",
        "May": "Haratua",
        "June": "Pipiri",
        "July": "Hōngoingoi",
        "August": "Here-turi-kōkā",
        "September": "Mahuru",
        "October": "Whiringa-ā-nuku",
        "November": "Whiringa-ā-rangi",
        "December": "Hakihea",
        "Jan_Abbr": "Kohi",
        "Feb_Abbr": "Hui",
        "Mar_Abbr": "Pou",
        "Apr_Abbr": "Pae",
        "May_Abbr": "Hara",
        "Jun_Abbr": "Pipi",
        "Jul_Abbr": "Hōngoi",
        "Aug_Abbr": "Here",
        "Sep_Abbr": "Mahu",
        "Oct_Abbr": "Whi-nu",
        "Nov_Abbr": "Whi-ra",
        "Dec_Abbr": "Haki",
        "AM": "a.m.",
        "PM": "p.m.",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d MMMM yyyy",
        "h:mm tt": "h:mm:ss tt",
        "h:mm:ss tt": "h:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d MMMM yyyy h:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "kohi(-tātea)?",
        "/feb(ruary)?/": "hui(-tanguru)?",
        "/mar(ch)?/": "pou(tū-te-rangi)?",
        "/apr(il)?/": "pae(nga-whāwhā)?",
        "/may/": "hara(tua)?",
        "/jun(e)?/": "pipi(ri)?",
        "/jul(y)?/": "hōngoi(ngoi)?",
        "/aug(ust)?/": "here(-turi-kōkā)?",
        "/sep(t(ember)?)?/": "mahu(ru)?",
        "/oct(ober)?/": "whiringa-ā-nuku",
        "/nov(ember)?/": "whiringa-ā-rangi",
        "/dec(ember)?/": "haki(hea)?",
        "/^su(n(day)?)?/": "^rātapu",
        "/^mo(n(day)?)?/": "^mane",
        "/^tu(e(s(day)?)?)?/": "^tūrei",
        "/^we(d(nesday)?)?/": "^wenerei",
        "/^th(u(r(s(day)?)?)?)?/": "^tāite",
        "/^fr(i(day)?)?/": "^paraire",
        "/^sa(t(urday)?)?/": "^hātarei",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "mi-NZ";
