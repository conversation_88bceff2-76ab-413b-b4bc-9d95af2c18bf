/* 
 * DateJS Culture String File
 * Country Code: cs-CZ
 * Name: Czech (Czech Republic)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["cs-CZ"] = {
        "name": "cs-CZ",
        "englishName": "Czech (Czech Republic)",
        "nativeName": "<PERSON><PERSON><PERSON><PERSON> (Česká republika)",
        "Sunday": "neděle",
        "Monday": "pondělí",
        "Tuesday": "úterý",
        "Wednesday": "středa",
        "Thursday": "čtvrtek",
        "Friday": "pátek",
        "Saturday": "sobota",
        "Sun": "ne",
        "Mon": "po",
        "Tue": "út",
        "Wed": "st",
        "Thu": "čt",
        "Fri": "pá",
        "Sat": "so",
        "Su": "ne",
        "Mo": "po",
        "Tu": "út",
        "We": "st",
        "Th": "čt",
        "Fr": "pá",
        "Sa": "so",
        "S_Sun_Initial": "n",
        "M_Mon_Initial": "p",
        "T_Tue_Initial": "ú",
        "W_Wed_Initial": "s",
        "T_Thu_Initial": "č",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "s",
        "January": "leden",
        "February": "únor",
        "March": "březen",
        "April": "duben",
        "May": "květen",
        "June": "červen",
        "July": "červenec",
        "August": "srpen",
        "September": "září",
        "October": "říjen",
        "November": "listopad",
        "December": "prosinec",
        "Jan_Abbr": "I",
        "Feb_Abbr": "II",
        "Mar_Abbr": "III",
        "Apr_Abbr": "IV",
        "May_Abbr": "V",
        "Jun_Abbr": "VI",
        "Jul_Abbr": "VII",
        "Aug_Abbr": "VIII",
        "Sep_Abbr": "IX",
        "Oct_Abbr": "X",
        "Nov_Abbr": "XI",
        "Dec_Abbr": "XII",
        "AM": "dop.",
        "PM": "odp.",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "leden",
        "/feb(ruary)?/": "únor",
        "/mar(ch)?/": "březen",
        "/apr(il)?/": "duben",
        "/may/": "květen",
        "/jun(e)?/": "červen",
        "/jul(y)?/": "červenec",
        "/aug(ust)?/": "srpen",
        "/sep(t(ember)?)?/": "září",
        "/oct(ober)?/": "říjen",
        "/nov(ember)?/": "listopad",
        "/dec(ember)?/": "prosinec",
        "/^su(n(day)?)?/": "^neděle",
        "/^mo(n(day)?)?/": "^pondělí",
        "/^tu(e(s(day)?)?)?/": "^úterý",
        "/^we(d(nesday)?)?/": "^středa",
        "/^th(u(r(s(day)?)?)?)?/": "^čtvrtek",
        "/^fr(i(day)?)?/": "^pátek",
        "/^sa(t(urday)?)?/": "^sobota",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "cs-CZ";
