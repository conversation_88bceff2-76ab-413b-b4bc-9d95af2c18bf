/* 
 * DateJS Culture String File
 * Country Code: bs-Latn-BA
 * Name: Bosnian (Bosnia and Herzegovina)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["bs-Latn-BA"] = {
        "name": "bs-Latn-BA",
        "englishName": "Bosnian (Bosnia and Herzegovina)",
        "nativeName": "b<PERSON><PERSON><PERSON> (Bosna i Hercegovina)",
        "Sunday": "nedjelja",
        "Monday": "ponedjeljak",
        "Tuesday": "utorak",
        "Wednesday": "srijeda",
        "Thursday": "četvrtak",
        "Friday": "petak",
        "Saturday": "subota",
        "Sun": "ned",
        "Mon": "pon",
        "Tue": "uto",
        "Wed": "sri",
        "Thu": "čet",
        "Fri": "pet",
        "Sat": "sub",
        "Su": "ned",
        "<PERSON>": "pon",
        "Tu": "uto",
        "We": "sri",
        "Th": "čet",
        "Fr": "pet",
        "Sa": "sub",
        "S_Sun_Initial": "n",
        "M_Mon_Initial": "p",
        "T_Tue_Initial": "u",
        "W_Wed_Initial": "s",
        "T_Thu_Initial": "č",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "s",
        "January": "januar",
        "February": "februar",
        "March": "mart",
        "April": "april",
        "May": "maj",
        "June": "jun",
        "July": "jul",
        "August": "avgust",
        "September": "septembar",
        "October": "oktobar",
        "November": "novembar",
        "December": "decembar",
        "Jan_Abbr": "jan",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "apr",
        "May_Abbr": "maj",
        "Jun_Abbr": "jun",
        "Jul_Abbr": "jul",
        "Aug_Abbr": "avg",
        "Sep_Abbr": "sep",
        "Oct_Abbr": "okt",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dec",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "H:mm:ss",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(uar)?",
        "/feb(ruary)?/": "feb(ruar)?",
        "/mar(ch)?/": "mar(t)?",
        "/apr(il)?/": "apr(il)?",
        "/may/": "maj",
        "/jun(e)?/": "jun",
        "/jul(y)?/": "jul",
        "/aug(ust)?/": "avg(ust)?",
        "/sep(t(ember)?)?/": "sep(tembar)?",
        "/oct(ober)?/": "okt(obar)?",
        "/nov(ember)?/": "nov(embar)?",
        "/dec(ember)?/": "dec(embar)?",
        "/^su(n(day)?)?/": "^nedjelja",
        "/^mo(n(day)?)?/": "^ponedjeljak",
        "/^tu(e(s(day)?)?)?/": "^utorak",
        "/^we(d(nesday)?)?/": "^srijeda",
        "/^th(u(r(s(day)?)?)?)?/": "^četvrtak",
        "/^fr(i(day)?)?/": "^petak",
        "/^sa(t(urday)?)?/": "^subota",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "bs-Latn-BA";
