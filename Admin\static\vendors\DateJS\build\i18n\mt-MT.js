/* 
 * DateJS Culture String File
 * Country Code: mt-MT
 * Name: Maltese (Malta)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["mt-MT"] = {
        "name": "mt-MT",
        "englishName": "Maltese (Malta)",
        "nativeName": "Malti (Malta)",
        "Sunday": "Il-Ħadd",
        "Monday": "It-Tnejn",
        "Tuesday": "It-Tlieta",
        "Wednesday": "L-Erbgħa",
        "Thursday": "Il-Ħamis",
        "Friday": "Il-Ġimgħa",
        "Saturday": "Is-Sibt",
        "Sun": "Ħad",
        "Mon": "Tne",
        "Tue": "Tli",
        "Wed": "Erb",
        "Thu": "Ħam",
        "Fri": "Ġim",
        "Sat": "Sib",
        "Su": "Ħad",
        "Mo": "Tne",
        "Tu": "T<PERSON>",
        "We": "Erb",
        "Th": "Ħam",
        "Fr": "Ġim",
        "Sa": "Sib",
        "S_Sun_Initial": "Ħ",
        "M_Mon_Initial": "T",
        "T_Tue_Initial": "T",
        "W_Wed_Initial": "E",
        "T_Thu_Initial": "Ħ",
        "F_Fri_Initial": "Ġ",
        "S_Sat_Initial": "S",
        "January": "Jannar",
        "February": "Frar",
        "March": "Marzu",
        "April": "April",
        "May": "Mejju",
        "June": "Ġunju",
        "July": "Lulju",
        "August": "Awissu",
        "September": "Settembru",
        "October": "Ottubru",
        "November": "Novembru",
        "December": "Diċembru",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Fra",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Apr",
        "May_Abbr": "Mej",
        "Jun_Abbr": "Ġun",
        "Jul_Abbr": "Lul",
        "Aug_Abbr": "Awi",
        "Sep_Abbr": "Set",
        "Oct_Abbr": "Ott",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Diċ",
        "AM": "AM",
        "PM": "PM",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d' ta' 'MMMM yyyy",
        "h:mm tt": "HH:mm:ss",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d' ta' 'MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(nar)?",
        "/feb(ruary)?/": "fra(r)?",
        "/mar(ch)?/": "mar(zu)?",
        "/apr(il)?/": "apr(il)?",
        "/may/": "mej(ju)?",
        "/jun(e)?/": "ġun(ju)?",
        "/jul(y)?/": "lul(ju)?",
        "/aug(ust)?/": "awi(ssu)?",
        "/sep(t(ember)?)?/": "set(tembru)?",
        "/oct(ober)?/": "ott(ubru)?",
        "/nov(ember)?/": "nov(embru)?",
        "/dec(ember)?/": "diċ(embru)?",
        "/^su(n(day)?)?/": "^il-ħadd",
        "/^mo(n(day)?)?/": "^it-tnejn",
        "/^tu(e(s(day)?)?)?/": "^it-tlieta",
        "/^we(d(nesday)?)?/": "^l-erbgħa",
        "/^th(u(r(s(day)?)?)?)?/": "^il-ħamis",
        "/^fr(i(day)?)?/": "^il-ġimgħa",
        "/^sa(t(urday)?)?/": "^is-sibt",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "mt-MT";
