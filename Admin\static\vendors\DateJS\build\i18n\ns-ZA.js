/* 
 * DateJS Culture String File
 * Country Code: ns-ZA
 * Name: Northern Sotho (South Africa)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ns-ZA"] = {
        "name": "ns-ZA",
        "englishName": "Northern Sotho (South Africa)",
        "nativeName": "Sesotho sa Leboa (Afrika Borwa)",
        "Sunday": "Lamorena",
        "Monday": "Mošupologo",
        "Tuesday": "Labobedi",
        "Wednesday": "Laboraro",
        "Thursday": "Labone",
        "Friday": "Labohlano",
        "Saturday": "Mokibelo",
        "Sun": "Sun",
        "Mon": "Mon",
        "Tue": "Tue",
        "Wed": "Wed",
        "Thu": "Thu",
        "Fri": "Fri",
        "Sat": "Sat",
        "Su": "Sun",
        "Mo": "Mon",
        "<PERSON>": "<PERSON><PERSON>",
        "We": "Wed",
        "Th": "Thu",
        "Fr": "Fri",
        "Sa": "Sat",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "M",
        "T_Tue_Initial": "T",
        "W_Wed_Initial": "W",
        "T_Thu_Initial": "T",
        "F_Fri_Initial": "F",
        "S_Sat_Initial": "S",
        "January": "Pherekgong",
        "February": "Hlakola",
        "March": "Mopitlo",
        "April": "Moranang",
        "May": "Mosegamanye",
        "June": "Ngoatobošego",
        "July": "Phuphu",
        "August": "Phato",
        "September": "Lewedi",
        "October": "Diphalana",
        "November": "Dibatsela",
        "December": "Manthole",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Feb",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Apr",
        "May_Abbr": "May",
        "Jun_Abbr": "Jun",
        "Jul_Abbr": "Jul",
        "Aug_Abbr": "Aug",
        "Sep_Abbr": "Sep",
        "Oct_Abbr": "Oct",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Dec",
        "AM": "AM",
        "PM": "PM",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy/MM/dd",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "hh:mm:ss tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "pherekgong",
        "/feb(ruary)?/": "hlakola",
        "/mar(ch)?/": "mopitlo",
        "/apr(il)?/": "moranang",
        "/may/": "mosegamanye",
        "/jun(e)?/": "ngoatobošego",
        "/jul(y)?/": "phuphu",
        "/aug(ust)?/": "phato",
        "/sep(t(ember)?)?/": "lewedi",
        "/oct(ober)?/": "diphalana",
        "/nov(ember)?/": "dibatsela",
        "/dec(ember)?/": "manthole",
        "/^su(n(day)?)?/": "^lamorena",
        "/^mo(n(day)?)?/": "^mošupologo",
        "/^tu(e(s(day)?)?)?/": "^labobedi",
        "/^we(d(nesday)?)?/": "^laboraro",
        "/^th(u(r(s(day)?)?)?)?/": "^labone",
        "/^fr(i(day)?)?/": "^labohlano",
        "/^sa(t(urday)?)?/": "^mokibelo",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ns-ZA";
