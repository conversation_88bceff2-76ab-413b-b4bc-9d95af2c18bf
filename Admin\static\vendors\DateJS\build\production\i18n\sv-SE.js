/* 
 * DateJS Culture String File
 * Country Code: sv-SE
 * Name: Swedish (Sweden)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sv-SE"] = {
        "name": "sv-SE",
        "englishName": "Swedish (Sweden)",
        "nativeName": "Svenska (Sverige)",
        "Sunday": "Söndag",
        "Monday": "Måndag",
        "Tuesday": "Tisdag",
        "Wednesday": "Onsdag",
        "Thursday": "Torsdag",
        "Friday": "Fredag",
        "Saturday": "Lördag",
        "Sun": "<PERSON>ön",
        "Mon": "<PERSON>ån",
        "Tue": "Tis",
        "Wed": "Ons",
        "Thu": "Tor",
        "Fri": "Fre",
        "Sat": "Lör",
        "Su": "Sö",
        "Mo": "Må",
        "Tu": "Ti",
        "We": "On",
        "Th": "To",
        "Fr": "Fr",
        "Sa": "Lö",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "M",
        "T_Tue_Initial": "T",
        "W_Wed_Initial": "O",
        "T_Thu_Initial": "T",
        "F_Fri_Initial": "F",
        "S_Sat_Initial": "L",
        "January": "Januari",
        "February": "Februari",
        "March": "Mars",
        "April": "April",
        "May": "Maj",
        "June": "Juni",
        "July": "Juli",
        "August": "Augusti",
        "September": "September",
        "October": "Oktober",
        "November": "November",
        "December": "December",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Feb",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Apr",
        "May_Abbr": "Maj",
        "Jun_Abbr": "Jun",
        "Jul_Abbr": "Jul",
        "Aug_Abbr": "Aug",
        "Sep_Abbr": "Sep",
        "Oct_Abbr": "Okt",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Dec",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy-MM-dd",
        "dddd, MMMM dd, yyyy": "'den 'd MMMM yyyy",
        "h:mm tt": "HH.mm",
        "h:mm:ss tt": "HH.mm.ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "'den 'd MMMM yyyy HH.mm.ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH.mm.ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH.mm.ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH.mm.ss",
        "MMMM dd": "'den 'd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(uari)?",
        "/feb(ruary)?/": "feb(ruari)?",
        "/mar(ch)?/": "mar(s)?",
        "/apr(il)?/": "apr(il)?",
        "/may/": "maj",
        "/jun(e)?/": "jun(i)?",
        "/jul(y)?/": "jul(i)?",
        "/aug(ust)?/": "aug(usti)?",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "okt(ober)?",
        "/nov(ember)?/": "nov(ember)?",
        "/dec(ember)?/": "dec(ember)?",
        "/^su(n(day)?)?/": "^sö(n(dag)?)?",
        "/^mo(n(day)?)?/": "^må(n(dag)?)?",
        "/^tu(e(s(day)?)?)?/": "^ti(s(dag)?)?",
        "/^we(d(nesday)?)?/": "^on(s(dag)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^to(r(s(dag)?)?)?",
        "/^fr(i(day)?)?/": "^fr(e(dag)?)?",
        "/^sa(t(urday)?)?/": "^lö(r(dag)?)?",
        "/^next/": "^nästa",
        "/^last|past|prev(ious)?/": "^föregående|förra|senaste",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|efter|från)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|före|tidigare)",
        "/^yes(terday)?/": "^i\\s?går|(för)går(dag)?",
        "/^t(od(ay)?)?/": "^i\\s?dag?",
        "/^tom(orrow)?/": "^i\\s?morgon|morgon(dag)?",
        "/^n(ow)?/": "^nu",
        "/^ms|milli(second)?s?/": "^ms|milli(sekund)?(er)?",
        "/^sec(ond)?s?/": "^sek(und)?(er)?",
        "/^mn|min(ute)?s?/": "^min(ut)?(er)?",
        "/^h(our)?s?/": "^t(im)?(ar)?",
        "/^w(eek)?s?/": "^v(eck(a)?)?(or)?",
        "/^m(onth)?s?/": "^m(ånad)?(er)?",
        "/^d(ay)?s?/": "^d(ag)?(ar)?",
        "/^y(ear)?s?/": "^å(r)?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sv-SE";
