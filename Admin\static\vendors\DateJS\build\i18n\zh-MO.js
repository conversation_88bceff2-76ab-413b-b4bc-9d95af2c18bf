/* 
 * DateJS Culture String File
 * Country Code: zh-MO
 * Name: Chinese (Macao S.A.R.)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["zh-MO"] = {
        "name": "zh-MO",
        "englishName": "Chinese (Macao S.A.R.)",
        "nativeName": "中文(澳門特别行政區)",
        "Sunday": "星期日",
        "Monday": "星期一",
        "Tuesday": "星期二",
        "Wednesday": "星期三",
        "Thursday": "星期四",
        "Friday": "星期五",
        "Saturday": "星期六",
        "Sun": "星期日",
        "Mon": "星期一",
        "Tue": "星期二",
        "Wed": "星期三",
        "Thu": "星期四",
        "Fri": "星期五",
        "Sat": "星期六",
        "Su": "日",
        "Mo": "一",
        "Tu": "二",
        "We": "三",
        "Th": "四",
        "Fr": "五",
        "Sa": "六",
        "S_Sun_Initial": "日",
        "M_Mon_Initial": "一",
        "T_Tue_Initial": "二",
        "W_Wed_Initial": "三",
        "T_Thu_Initial": "四",
        "F_Fri_Initial": "五",
        "S_Sat_Initial": "六",
        "January": "一月",
        "February": "二月",
        "March": "三月",
        "April": "四月",
        "May": "五月",
        "June": "六月",
        "July": "七月",
        "August": "八月",
        "September": "九月",
        "October": "十月",
        "November": "十一月",
        "December": "十二月",
        "Jan_Abbr": "一月",
        "Feb_Abbr": "二月",
        "Mar_Abbr": "三月",
        "Apr_Abbr": "四月",
        "May_Abbr": "五月",
        "Jun_Abbr": "六月",
        "Jul_Abbr": "七月",
        "Aug_Abbr": "八月",
        "Sep_Abbr": "九月",
        "Oct_Abbr": "十月",
        "Nov_Abbr": "十一月",
        "Dec_Abbr": "十二月",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d/M/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d MMMM, yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d MMMM, yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "一月",
        "/feb(ruary)?/": "二月",
        "/mar(ch)?/": "三月",
        "/apr(il)?/": "四月",
        "/may/": "五月",
        "/jun(e)?/": "六月",
        "/jul(y)?/": "七月",
        "/aug(ust)?/": "八月",
        "/sep(t(ember)?)?/": "九月",
        "/oct(ober)?/": "十月",
        "/nov(ember)?/": "十一月",
        "/dec(ember)?/": "十二月",
        "/^su(n(day)?)?/": "^星期日",
        "/^mo(n(day)?)?/": "^星期一",
        "/^tu(e(s(day)?)?)?/": "^星期二",
        "/^we(d(nesday)?)?/": "^星期三",
        "/^th(u(r(s(day)?)?)?)?/": "^星期四",
        "/^fr(i(day)?)?/": "^星期五",
        "/^sa(t(urday)?)?/": "^星期六",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "zh-MO";
