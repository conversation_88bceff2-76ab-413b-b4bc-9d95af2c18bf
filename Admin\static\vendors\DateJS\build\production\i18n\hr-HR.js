/* 
 * DateJS Culture String File
 * Country Code: hr-HR
 * Name: Croatian (Croatia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["hr-HR"] = {
        "name": "hr-HR",
        "englishName": "Croatian (Croatia)",
        "nativeName": "hrvatski (Hrvatska)",
        "Sunday": "nedjelja",
        "Monday": "ponedjeljak",
        "Tuesday": "utorak",
        "Wednesday": "srijeda",
        "Thursday": "četvrtak",
        "Friday": "petak",
        "Saturday": "subota",
        "Sun": "ned",
        "Mon": "pon",
        "Tue": "uto",
        "Wed": "sri",
        "Thu": "čet",
        "Fri": "pet",
        "Sat": "sub",
        "Su": "ne",
        "Mo": "po",
        "Tu": "ut",
        "We": "sr",
        "Th": "če",
        "Fr": "pe",
        "Sa": "su",
        "S_Sun_Initial": "n",
        "M_Mon_Initial": "p",
        "T_Tue_Initial": "u",
        "W_Wed_Initial": "s",
        "T_Thu_Initial": "č",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "s",
        "January": "siječanj",
        "February": "veljača",
        "March": "ožujak",
        "April": "travanj",
        "May": "svibanj",
        "June": "lipanj",
        "July": "srpanj",
        "August": "kolovoz",
        "September": "rujan",
        "October": "listopad",
        "November": "studeni",
        "December": "prosinac",
        "Jan_Abbr": "sij",
        "Feb_Abbr": "vlj",
        "Mar_Abbr": "ožu",
        "Apr_Abbr": "tra",
        "May_Abbr": "svi",
        "Jun_Abbr": "lip",
        "Jul_Abbr": "srp",
        "Aug_Abbr": "kol",
        "Sep_Abbr": "ruj",
        "Oct_Abbr": "lis",
        "Nov_Abbr": "stu",
        "Dec_Abbr": "pro",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "sij(ečanj)?",
        "/feb(ruary)?/": "veljača",
        "/mar(ch)?/": "ožu(jak)?",
        "/apr(il)?/": "tra(vanj)?",
        "/may/": "svi(banj)?",
        "/jun(e)?/": "lip(anj)?",
        "/jul(y)?/": "srp(anj)?",
        "/aug(ust)?/": "kol(ovoz)?",
        "/sep(t(ember)?)?/": "ruj(an)?",
        "/oct(ober)?/": "lis(topad)?",
        "/nov(ember)?/": "stu(deni)?",
        "/dec(ember)?/": "pro(sinac)?",
        "/^su(n(day)?)?/": "^ne(d(jelja)?)?",
        "/^mo(n(day)?)?/": "^po(n(edjeljak)?)?",
        "/^tu(e(s(day)?)?)?/": "^ut(o(rak)?)?",
        "/^we(d(nesday)?)?/": "^sr(i(jeda)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^če(t(vrtak)?)?",
        "/^fr(i(day)?)?/": "^pe(t(ak)?)?",
        "/^sa(t(urday)?)?/": "^su(b(ota)?)?",
        "/^next/": "^slijedeć(i|e|eg)",
        "/^last|past|prev(ious)?/": "^zadnji|posljednji|prethodni",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|pos(lije)?|od|odsad(a)?)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|pr(ije)?pred)",
        "/^yes(terday)?/": "^jučer",
        "/^t(od(ay)?)?/": "^danas",
        "/^tom(orrow)?/": "^sutra",
        "/^n(ow)?/": "^sad(a)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sek(und(a|e|i)?)?",
        "/^mn|min(ute)?s?/": "^mn|min(ut(a|e|i)?)?",
        "/^h(our)?s?/": "^s(at(a|i)?)?",
        "/^w(eek)?s?/": "^tj(edan(a|i)?)?",
        "/^m(onth)?s?/": "^mj(esec(a|i)?)?",
        "/^d(ay)?s?/": "^dan(a|i)?",
        "/^y(ear)?s?/": "^god(in(a|e|i|u))?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "hr-HR";
