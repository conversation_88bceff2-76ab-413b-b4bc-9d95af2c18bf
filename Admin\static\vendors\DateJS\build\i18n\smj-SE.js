/* 
 * DateJS Culture String File
 * Country Code: smj-SE
 * Name: <PERSON> (<PERSON><PERSON>) (Sweden)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["smj-SE"] = {
        "name": "smj-SE",
        "englishName": "<PERSON> (<PERSON><PERSON>) (Sweden)",
        "nativeName": "julevusámegiella (Svierik)",
        "Sunday": "ájllek",
        "Monday": "mánnodahka",
        "Tuesday": "dijstahka",
        "Wednesday": "gasskavahkko",
        "Thursday": "duoras<PERSON>ka",
        "Friday": "bierjjedahka",
        "Saturday": "lávvodahka",
        "Sun": "ájl",
        "Mon": "mán",
        "Tue": "dis",
        "Wed": "gas",
        "Thu": "duor",
        "Fri": "bier",
        "Sat": "láv",
        "Su": "ájl",
        "Mo": "mán",
        "Tu": "dis",
        "We": "gas",
        "Th": "duor",
        "Fr": "bier",
        "Sa": "láv",
        "S_Sun_Initial": "á",
        "M_Mon_Initial": "m",
        "T_Tue_Initial": "d",
        "W_Wed_Initial": "g",
        "T_Thu_Initial": "d",
        "F_Fri_Initial": "b",
        "S_Sat_Initial": "l",
        "January": "ådåjakmánno",
        "February": "guovvamánno",
        "March": "sjnjuktjamánno",
        "April": "vuoratjismánno",
        "May": "moarmesmánno",
        "June": "biehtsemánno",
        "July": "sjnjilltjamánno",
        "August": "bårggemánno",
        "September": "ragátmánno",
        "October": "gålgådismánno",
        "November": "basádismánno",
        "December": "javllamánno",
        "Jan_Abbr": "ådåj",
        "Feb_Abbr": "guov",
        "Mar_Abbr": "snju",
        "Apr_Abbr": "vuor",
        "May_Abbr": "moar",
        "Jun_Abbr": "bieh",
        "Jul_Abbr": "snji",
        "Aug_Abbr": "bårg",
        "Sep_Abbr": "ragá",
        "Oct_Abbr": "gålg",
        "Nov_Abbr": "basá",
        "Dec_Abbr": "javl",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy-MM-dd",
        "dddd, MMMM dd, yyyy": "MMMM d'. b. 'yyyy",
        "h:mm tt": "HH:mm:ss",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "MMMM d'. b. 'yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ådåj(akmánno)?",
        "/feb(ruary)?/": "guov(vamánno)?",
        "/mar(ch)?/": "sjnjuktjamánno",
        "/apr(il)?/": "vuor(atjismánno)?",
        "/may/": "moar(mesmánno)?",
        "/jun(e)?/": "bieh(tsemánno)?",
        "/jul(y)?/": "sjnjilltjamánno",
        "/aug(ust)?/": "bårg(gemánno)?",
        "/sep(t(ember)?)?/": "ragá(tmánno)?",
        "/oct(ober)?/": "gålg(ådismánno)?",
        "/nov(ember)?/": "basá(dismánno)?",
        "/dec(ember)?/": "javl(lamánno)?",
        "/^su(n(day)?)?/": "^ájllek",
        "/^mo(n(day)?)?/": "^mánnodahka",
        "/^tu(e(s(day)?)?)?/": "^dijstahka",
        "/^we(d(nesday)?)?/": "^gasskavahkko",
        "/^th(u(r(s(day)?)?)?)?/": "^duorastahka",
        "/^fr(i(day)?)?/": "^bierjjedahka",
        "/^sa(t(urday)?)?/": "^lávvodahka",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "smj-SE";
