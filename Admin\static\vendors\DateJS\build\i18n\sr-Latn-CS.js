/* 
 * DateJS Culture String File
 * Country Code: sr-Latn-CS
 * Name: Serbian (Latin, Serbia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sr-Latn-CS"] = {
        "name": "sr-Latn-CS",
        "englishName": "Serbian (Latin, Serbia)",
        "nativeName": "srpski (Srbija)",
        "Sunday": "nedelja",
        "Monday": "ponedeljak",
        "Tuesday": "utorak",
        "Wednesday": "sreda",
        "Thursday": "četvrtak",
        "Friday": "petak",
        "Saturday": "subota",
        "Sun": "ned",
        "Mon": "pon",
        "Tue": "uto",
        "Wed": "sre",
        "Thu": "čet",
        "Fri": "pet",
        "Sat": "sub",
        "Su": "ne",
        "Mo": "po",
        "Tu": "ut",
        "We": "sr",
        "Th": "če",
        "Fr": "pe",
        "Sa": "su",
        "S_Sun_Initial": "n",
        "M_Mon_Initial": "p",
        "T_Tue_Initial": "u",
        "W_Wed_Initial": "s",
        "T_Thu_Initial": "č",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "s",
        "January": "januar",
        "February": "februar",
        "March": "mart",
        "April": "april",
        "May": "maj",
        "June": "jun",
        "July": "jul",
        "August": "avgust",
        "September": "septembar",
        "October": "oktobar",
        "November": "novembar",
        "December": "decembar",
        "Jan_Abbr": "jan",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "apr",
        "May_Abbr": "maj",
        "Jun_Abbr": "jun",
        "Jul_Abbr": "jul",
        "Aug_Abbr": "avg",
        "Sep_Abbr": "sep",
        "Oct_Abbr": "okt",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dec",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(uar)?",
        "/feb(ruary)?/": "feb(ruar)?",
        "/mar(ch)?/": "mar(t)?",
        "/apr(il)?/": "apr(il)?",
        "/may/": "maj",
        "/jun(e)?/": "jun",
        "/jul(y)?/": "jul",
        "/aug(ust)?/": "avg(ust)?",
        "/sep(t(ember)?)?/": "sep(tembar)?",
        "/oct(ober)?/": "okt(obar)?",
        "/nov(ember)?/": "nov(embar)?",
        "/dec(ember)?/": "dec(embar)?",
        "/^su(n(day)?)?/": "^ne(d(elja)?)?",
        "/^mo(n(day)?)?/": "^po(n(edeljak)?)?",
        "/^tu(e(s(day)?)?)?/": "^ut(o(rak)?)?",
        "/^we(d(nesday)?)?/": "^sr(e(da)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^če(t(vrtak)?)?",
        "/^fr(i(day)?)?/": "^pe(t(ak)?)?",
        "/^sa(t(urday)?)?/": "^su(b(ota)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sr-Latn-CS";
