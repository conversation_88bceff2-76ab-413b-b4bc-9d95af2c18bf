/* 
 * DateJS Culture String File
 * Country Code: ar-SA
 * Name: Arabic (Saudi Arabia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ar-SA"] = {
        "name": "ar-SA",
        "englishName": "Arabic (Saudi Arabia)",
        "nativeName": "العربية (المملكة العربية السعودية)",
        "Sunday": "الاحد",
        "Monday": "الاثنين",
        "Tuesday": "الثلاثاء",
        "Wednesday": "الاربعاء",
        "Thursday": "الخميس",
        "Friday": "الجمعة",
        "Saturday": "السبت",
        "Sun": "الاحد",
        "Mon": "الاثنين",
        "Tue": "الثلاثاء",
        "Wed": "الاربعاء",
        "Thu": "الخميس",
        "Fri": "الجمعة",
        "Sat": "السبت",
        "Su": "ح",
        "Mo": "ن",
        "Tu": "ث",
        "We": "ر",
        "Th": "خ",
        "Fr": "ج",
        "Sa": "س",
        "S_Sun_Initial": "ح",
        "M_Mon_Initial": "ن",
        "T_Tue_Initial": "ث",
        "W_Wed_Initial": "ر",
        "T_Thu_Initial": "خ",
        "F_Fri_Initial": "ج",
        "S_Sat_Initial": "س",
        "January": "محرم",
        "February": "صفر",
        "March": "ربيع الأول",
        "April": "ربيع الثاني",
        "May": "جمادى الأولى",
        "June": "جمادى الثانية",
        "July": "رجب",
        "August": "شعبان",
        "September": "رمضان",
        "October": "شوال",
        "November": "ذو القعدة",
        "December": "ذو الحجة",
        "Jan_Abbr": "محرم",
        "Feb_Abbr": "صفر",
        "Mar_Abbr": "ربيع الاول",
        "Apr_Abbr": "ربيع الثاني",
        "May_Abbr": "جمادى الاولى",
        "Jun_Abbr": "جمادى الثانية",
        "Jul_Abbr": "رجب",
        "Aug_Abbr": "شعبان",
        "Sep_Abbr": "رمضان",
        "Oct_Abbr": "شوال",
        "Nov_Abbr": "ذو القعدة",
        "Dec_Abbr": "ذو الحجة",
        "AM": "ص",
        "PM": "م",
        "firstDayOfWeek": 6,
        "twoDigitYearMax": 1451,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yy",
        "dddd, MMMM dd, yyyy": "dd/MMMM/yyyy",
        "h:mm tt": "hh:mm tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd/MMMM/yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "محرم",
        "/feb(ruary)?/": "صفر",
        "/mar(ch)?/": "ربيع الأول",
        "/apr(il)?/": "ربيع الثاني",
        "/may/": "جمادى الأولى",
        "/jun(e)?/": "جمادى الثانية",
        "/jul(y)?/": "رجب",
        "/aug(ust)?/": "شعبان",
        "/sep(t(ember)?)?/": "رمضان",
        "/oct(ober)?/": "شوال",
        "/nov(ember)?/": "ذو القعدة",
        "/dec(ember)?/": "ذو الحجة",
        "/^su(n(day)?)?/": "^الاحد",
        "/^mo(n(day)?)?/": "^الاثنين",
        "/^tu(e(s(day)?)?)?/": "^الثلاثاء",
        "/^we(d(nesday)?)?/": "^الاربعاء",
        "/^th(u(r(s(day)?)?)?)?/": "^الخميس",
        "/^fr(i(day)?)?/": "^الجمعة",
        "/^sa(t(urday)?)?/": "^السبت",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ar-SA";
