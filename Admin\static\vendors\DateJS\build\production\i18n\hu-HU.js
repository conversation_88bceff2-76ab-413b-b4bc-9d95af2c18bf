/* 
 * DateJS Culture String File
 * Country Code: hu-HU
 * Name: Hungarian (Hungary)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["hu-HU"] = {
        "name": "hu-HU",
        "englishName": "Hungarian (Hungary)",
        "nativeName": "magyar (Magyarország)",
        "Sunday": "vasárnap",
        "Monday": "hétfő",
        "Tuesday": "kedd",
        "Wednesday": "szerda",
        "Thursday": "csütörtök",
        "Friday": "péntek",
        "Saturday": "szombat",
        "Sun": "V",
        "Mon": "H",
        "Tue": "K",
        "Wed": "Sze",
        "Thu": "Cs",
        "Fri": "P",
        "Sat": "Szo",
        "Su": "V",
        "Mo": "H",
        "Tu": "K",
        "We": "<PERSON><PERSON>",
        "Th": "Cs",
        "Fr": "P",
        "Sa": "Szo",
        "S_Sun_Initial": "V",
        "M_Mon_Initial": "H",
        "T_Tue_Initial": "K",
        "W_Wed_Initial": "S",
        "T_Thu_Initial": "C",
        "F_Fri_Initial": "P",
        "S_Sat_Initial": "S",
        "January": "január",
        "February": "február",
        "March": "március",
        "April": "április",
        "May": "május",
        "June": "június",
        "July": "július",
        "August": "augusztus",
        "September": "szeptember",
        "October": "október",
        "November": "november",
        "December": "december",
        "Jan_Abbr": "jan.",
        "Feb_Abbr": "febr.",
        "Mar_Abbr": "márc.",
        "Apr_Abbr": "ápr.",
        "May_Abbr": "máj.",
        "Jun_Abbr": "jún.",
        "Jul_Abbr": "júl.",
        "Aug_Abbr": "aug.",
        "Sep_Abbr": "szept.",
        "Oct_Abbr": "okt.",
        "Nov_Abbr": "nov.",
        "Dec_Abbr": "dec.",
        "AM": "de.",
        "PM": "du.",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy. MM. dd.",
        "dddd, MMMM dd, yyyy": "yyyy. MMMM d.",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy. MMMM d. H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM d.",
        "MMMM, yyyy": "yyyy. MMMM",
        "/jan(uary)?/": "jan(.(uár)?)?",
        "/feb(ruary)?/": "febr(.(uár)?)?",
        "/mar(ch)?/": "márc(.(ius)?)?",
        "/apr(il)?/": "ápr(.(ilis)?)?",
        "/may/": "máj(.(us)?)?",
        "/jun(e)?/": "jún(.(ius)?)?",
        "/jul(y)?/": "júl(.(ius)?)?",
        "/aug(ust)?/": "aug(.(usztus)?)?",
        "/sep(t(ember)?)?/": "szept(.(ember)?)?",
        "/oct(ober)?/": "okt(.(óber)?)?",
        "/nov(ember)?/": "nov(.(ember)?)?",
        "/dec(ember)?/": "dec(.(ember)?)?",
        "/^su(n(day)?)?/": "^vasárnap",
        "/^mo(n(day)?)?/": "^hétfő",
        "/^tu(e(s(day)?)?)?/": "^kedd",
        "/^we(d(nesday)?)?/": "^szerda",
        "/^th(u(r(s(day)?)?)?)?/": "^csütörtök",
        "/^fr(i(day)?)?/": "^péntek",
        "/^sa(t(urday)?)?/": "^szombat",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "hu-HU";
