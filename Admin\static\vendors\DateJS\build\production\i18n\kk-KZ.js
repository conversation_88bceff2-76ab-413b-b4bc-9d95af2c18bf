/* 
 * DateJS Culture String File
 * Country Code: kk-KZ
 * Name: Kazakh (Kazakhstan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["kk-KZ"] = {
        "name": "kk-KZ",
        "englishName": "Kazakh (Kazakhstan)",
        "nativeName": "Қазақ (Қазақстан)",
        "Sunday": "Жексенбі",
        "Monday": "Дүйсенбі",
        "Tuesday": "Сейсенбі",
        "Wednesday": "Сәрсенбі",
        "Thursday": "Бейсенбі",
        "Friday": "Жұма",
        "Saturday": "Сенбі",
        "Sun": "Жк",
        "Mon": "Дс",
        "Tue": "Сс",
        "Wed": "Ср",
        "Thu": "Бс",
        "Fri": "Жм",
        "Sat": "Сн",
        "Su": "Жк",
        "Mo": "Дс",
        "<PERSON>": "Сс",
        "We": "Ср",
        "Th": "Бс",
        "Fr": "Жм",
        "Sa": "Сн",
        "S_Sun_Initial": "Ж",
        "M_Mon_Initial": "Д",
        "T_Tue_Initial": "С",
        "W_Wed_Initial": "С",
        "T_Thu_Initial": "Б",
        "F_Fri_Initial": "Ж",
        "S_Sat_Initial": "С",
        "January": "қаңтар",
        "February": "ақпан",
        "March": "наурыз",
        "April": "сәуір",
        "May": "мамыр",
        "June": "маусым",
        "July": "шілде",
        "August": "тамыз",
        "September": "қыркүйек",
        "October": "қазан",
        "November": "қараша",
        "December": "желтоқсан",
        "Jan_Abbr": "Қаң",
        "Feb_Abbr": "Ақп",
        "Mar_Abbr": "Нау",
        "Apr_Abbr": "Сәу",
        "May_Abbr": "Мам",
        "Jun_Abbr": "Мау",
        "Jul_Abbr": "Шіл",
        "Aug_Abbr": "Там",
        "Sep_Abbr": "Қыр",
        "Oct_Abbr": "Қаз",
        "Nov_Abbr": "Қар",
        "Dec_Abbr": "Жел",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy 'ж.'",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy 'ж.' H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "қаң(тар)?",
        "/feb(ruary)?/": "ақп(ан)?",
        "/mar(ch)?/": "нау(рыз)?",
        "/apr(il)?/": "сәу(ір)?",
        "/may/": "мам(ыр)?",
        "/jun(e)?/": "мау(сым)?",
        "/jul(y)?/": "шіл(де)?",
        "/aug(ust)?/": "там(ыз)?",
        "/sep(t(ember)?)?/": "қыр(күйек)?",
        "/oct(ober)?/": "қаз(ан)?",
        "/nov(ember)?/": "қар(аша)?",
        "/dec(ember)?/": "жел(тоқсан)?",
        "/^su(n(day)?)?/": "^жексенбі",
        "/^mo(n(day)?)?/": "^дүйсенбі",
        "/^tu(e(s(day)?)?)?/": "^сейсенбі",
        "/^we(d(nesday)?)?/": "^сәрсенбі",
        "/^th(u(r(s(day)?)?)?)?/": "^бейсенбі",
        "/^fr(i(day)?)?/": "^жұма",
        "/^sa(t(urday)?)?/": "^сенбі",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "kk-KZ";
