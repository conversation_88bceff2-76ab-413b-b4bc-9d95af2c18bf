/* 
 * DateJS Culture String File
 * Country Code: pl-PL
 * Name: Polish (Poland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["pl-PL"] = {
        "name": "pl-PL",
        "englishName": "Polish (Poland)",
        "nativeName": "polski (Polska)",
        "Sunday": "niedziela",
        "Monday": "poniedziałek",
        "Tuesday": "wtorek",
        "Wednesday": "środa",
        "Thursday": "czwartek",
        "Friday": "piątek",
        "Saturday": "sobota",
        "Sun": "N",
        "Mon": "Pn",
        "Tue": "Wt",
        "Wed": "Śr",
        "Thu": "Cz",
        "Fri": "Pt",
        "Sat": "So",
        "Su": "N",
        "Mo": "Pn",
        "Tu": "Wt",
        "We": "Śr",
        "Th": "Cz",
        "Fr": "Pt",
        "Sa": "So",
        "S_Sun_Initial": "N",
        "M_Mon_Initial": "P",
        "T_Tue_Initial": "W",
        "W_Wed_Initial": "Ś",
        "T_Thu_Initial": "C",
        "F_Fri_Initial": "P",
        "S_Sat_Initial": "S",
        "January": "styczeń",
        "February": "luty",
        "March": "marzec",
        "April": "kwiecień",
        "May": "maj",
        "June": "czerwiec",
        "July": "lipiec",
        "August": "sierpień",
        "September": "wrzesień",
        "October": "październik",
        "November": "listopad",
        "December": "grudzień",
        "Jan_Abbr": "sty",
        "Feb_Abbr": "lut",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "kwi",
        "May_Abbr": "maj",
        "Jun_Abbr": "cze",
        "Jul_Abbr": "lip",
        "Aug_Abbr": "sie",
        "Sep_Abbr": "wrz",
        "Oct_Abbr": "paź",
        "Nov_Abbr": "lis",
        "Dec_Abbr": "gru",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy-MM-dd",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "sty(czeń)?",
        "/feb(ruary)?/": "lut(y)?",
        "/mar(ch)?/": "mar(zec)?",
        "/apr(il)?/": "kwi(ecień)?",
        "/may/": "maj",
        "/jun(e)?/": "cze(rwiec)?",
        "/jul(y)?/": "lip(iec)?",
        "/aug(ust)?/": "sie(rpień)?",
        "/sep(t(ember)?)?/": "wrz(esień)?",
        "/oct(ober)?/": "paź(dziernik)?",
        "/nov(ember)?/": "lis(topad)?",
        "/dec(ember)?/": "gru(dzień)?",
        "/^su(n(day)?)?/": "^niedziela",
        "/^mo(n(day)?)?/": "^poniedziałek",
        "/^tu(e(s(day)?)?)?/": "^wtorek",
        "/^we(d(nesday)?)?/": "^środa",
        "/^th(u(r(s(day)?)?)?)?/": "^czwartek",
        "/^fr(i(day)?)?/": "^piątek",
        "/^sa(t(urday)?)?/": "^sobota",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "pl-PL";
