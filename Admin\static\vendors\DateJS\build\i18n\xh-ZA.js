/* 
 * DateJS Culture String File
 * Country Code: xh-ZA
 * Name: <PERSON>hos<PERSON> (South Africa)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["xh-ZA"] = {
        "name": "xh-ZA",
        "englishName": "Xhosa (South Africa)",
        "nativeName": "isiXhosa (uMzantsi Afrika)",
        "Sunday": "iCawa",
        "Monday": "uMvulo",
        "Tuesday": "uLwesibini",
        "Wednesday": "uLwesithathu",
        "Thursday": "uLwesine",
        "Friday": "uLwesihlanu",
        "Saturday": "uMgqibelo",
        "Sun": "Sun",
        "Mon": "Mon",
        "Tue": "Tue",
        "Wed": "Wed",
        "Thu": "Thu",
        "Fri": "Fri",
        "Sat": "Sat",
        "Su": "Sun",
        "Mo": "Mon",
        "Tu": "Tue",
        "We": "Wed",
        "Th": "Thu",
        "Fr": "Fri",
        "Sa": "Sat",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "M",
        "T_Tue_Initial": "T",
        "W_Wed_Initial": "W",
        "T_Thu_Initial": "T",
        "F_Fri_Initial": "F",
        "S_Sat_Initial": "S",
        "January": "eyoMqungu",
        "February": "eyoMdumba",
        "March": "eyoKwindla",
        "April": "Tshazimpuzi",
        "May": "Canzibe",
        "June": "eyeSilimela",
        "July": "eyeKhala",
        "August": "eyeThupha",
        "September": "eyoMsintsi",
        "October": "eyeDwara",
        "November": "eyeNkanga",
        "December": "eyoMnga",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Feb",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Apr",
        "May_Abbr": "May",
        "Jun_Abbr": "Jun",
        "Jul_Abbr": "Jul",
        "Aug_Abbr": "Aug",
        "Sep_Abbr": "Sep",
        "Oct_Abbr": "Oct",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Dec",
        "AM": "AM",
        "PM": "PM",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy/MM/dd",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "hh:mm:ss tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "eyomqungu",
        "/feb(ruary)?/": "eyomdumba",
        "/mar(ch)?/": "eyokwindla",
        "/apr(il)?/": "tshazimpuzi",
        "/may/": "canzibe",
        "/jun(e)?/": "eyesilimela",
        "/jul(y)?/": "eyekhala",
        "/aug(ust)?/": "eyethupha",
        "/sep(t(ember)?)?/": "eyomsintsi",
        "/oct(ober)?/": "eyedwara",
        "/nov(ember)?/": "eyenkanga",
        "/dec(ember)?/": "eyomnga",
        "/^su(n(day)?)?/": "^icawa",
        "/^mo(n(day)?)?/": "^umvulo",
        "/^tu(e(s(day)?)?)?/": "^ulwesibini",
        "/^we(d(nesday)?)?/": "^ulwesithathu",
        "/^th(u(r(s(day)?)?)?)?/": "^ulwesine",
        "/^fr(i(day)?)?/": "^ulwesihlanu",
        "/^sa(t(urday)?)?/": "^umgqibelo",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "xh-ZA";
