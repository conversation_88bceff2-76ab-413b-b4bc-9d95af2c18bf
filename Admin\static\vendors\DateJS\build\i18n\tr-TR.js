/* 
 * DateJS Culture String File
 * Country Code: tr-TR
 * Name: Turkish (Turkey)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["tr-TR"] = {
        "name": "tr-TR",
        "englishName": "Turkish (Turkey)",
        "nativeName": "Türkçe (Türkiye)",
        "Sunday": "Pazar",
        "Monday": "Pazartesi",
        "Tuesday": "Salı",
        "Wednesday": "Çarşamba",
        "Thursday": "Perşembe",
        "Friday": "Cuma",
        "Saturday": "Cumartesi",
        "Sun": "Paz",
        "Mon": "Pzt",
        "Tue": "Sal",
        "Wed": "Çar",
        "Thu": "Per",
        "Fri": "Cum",
        "Sat": "Cmt",
        "Su": "Pz",
        "Mo": "Pt",
        "Tu": "Sa",
        "We": "<PERSON><PERSON>",
        "Th": "Pe",
        "Fr": "Cu",
        "Sa": "Ct",
        "S_Sun_Initial": "P",
        "M_Mon_Initial": "P",
        "T_Tue_Initial": "S",
        "W_Wed_Initial": "Ç",
        "T_Thu_Initial": "P",
        "F_Fri_Initial": "C",
        "S_Sat_Initial": "C",
        "January": "Ocak",
        "February": "Şubat",
        "March": "Mart",
        "April": "Nisan",
        "May": "Mayıs",
        "June": "Haziran",
        "July": "Temmuz",
        "August": "Ağustos",
        "September": "Eylül",
        "October": "Ekim",
        "November": "Kasım",
        "December": "Aralık",
        "Jan_Abbr": "Oca",
        "Feb_Abbr": "Şub",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Nis",
        "May_Abbr": "May",
        "Jun_Abbr": "Haz",
        "Jul_Abbr": "Tem",
        "Aug_Abbr": "Ağu",
        "Sep_Abbr": "Eyl",
        "Oct_Abbr": "Eki",
        "Nov_Abbr": "Kas",
        "Dec_Abbr": "Ara",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy dddd",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy dddd HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "oca(k)?",
        "/feb(ruary)?/": "şub(at)?",
        "/mar(ch)?/": "mar(t)?",
        "/apr(il)?/": "nis(an)?",
        "/may/": "may(ıs)?",
        "/jun(e)?/": "haz(iran)?",
        "/jul(y)?/": "tem(muz)?",
        "/aug(ust)?/": "ağu(stos)?",
        "/sep(t(ember)?)?/": "eyl(ül)?",
        "/oct(ober)?/": "eki(m)?",
        "/nov(ember)?/": "kas(ım)?",
        "/dec(ember)?/": "ara(lık)?",
        "/^su(n(day)?)?/": "^pz(z(ar)?)?",
        "/^mo(n(day)?)?/": "^pt(t(artesi)?)?",
        "/^tu(e(s(day)?)?)?/": "^sa(l(ı)?)?",
        "/^we(d(nesday)?)?/": "^ça(r(şamba)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^pe(r(şembe)?)?",
        "/^fr(i(day)?)?/": "^cu(m(a)?)?",
        "/^sa(t(urday)?)?/": "^ct(t(artesi)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "tr-TR";
