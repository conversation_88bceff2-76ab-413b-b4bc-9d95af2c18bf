/* 
 * DateJS Culture String File
 * Country Code: eu-ES
 * Name: Basque (Basque)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["eu-ES"] = {
        "name": "eu-ES",
        "englishName": "Basque (Basque)",
        "nativeName": "euskara (euskara)",
        "Sunday": "igandea",
        "Monday": "astelehena",
        "Tuesday": "asteartea",
        "Wednesday": "asteazkena",
        "Thursday": "osteguna",
        "Friday": "ostirala",
        "Saturday": "larunbata",
        "Sun": "ig.",
        "Mon": "al.",
        "Tue": "as.",
        "Wed": "az.",
        "Thu": "og.",
        "Fri": "or.",
        "Sat": "lr.",
        "Su": "ig",
        "Mo": "al",
        "Tu": "as",
        "We": "az",
        "Th": "og",
        "Fr": "or",
        "Sa": "lr",
        "S_Sun_Initial": "i",
        "M_Mon_Initial": "a",
        "T_Tue_Initial": "a",
        "W_Wed_Initial": "a",
        "T_Thu_Initial": "o",
        "F_Fri_Initial": "o",
        "S_Sat_Initial": "l",
        "January": "urtarrila",
        "February": "otsaila",
        "March": "martxoa",
        "April": "apirila",
        "May": "maiatza",
        "June": "ekaina",
        "July": "uztaila",
        "August": "abuztua",
        "September": "iraila",
        "October": "urria",
        "November": "azaroa",
        "December": "abendua",
        "Jan_Abbr": "urt.",
        "Feb_Abbr": "ots.",
        "Mar_Abbr": "mar.",
        "Apr_Abbr": "api.",
        "May_Abbr": "mai.",
        "Jun_Abbr": "eka.",
        "Jul_Abbr": "uzt.",
        "Aug_Abbr": "abu.",
        "Sep_Abbr": "ira.",
        "Oct_Abbr": "urr.",
        "Nov_Abbr": "aza.",
        "Dec_Abbr": "abe.",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy/MM/dd",
        "dddd, MMMM dd, yyyy": "dddd, yyyy.'eko' MMMM'k 'd",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, yyyy.'eko' MMMM'k 'd HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "yyyy.'eko' MMMM",
        "/jan(uary)?/": "urt(.(arrila)?)?",
        "/feb(ruary)?/": "ots(.(aila)?)?",
        "/mar(ch)?/": "mar(.(txoa)?)?",
        "/apr(il)?/": "api(.(rila)?)?",
        "/may/": "mai(.(atza)?)?",
        "/jun(e)?/": "eka(.(ina)?)?",
        "/jul(y)?/": "uzt(.(aila)?)?",
        "/aug(ust)?/": "abu(.(ztua)?)?",
        "/sep(t(ember)?)?/": "ira(.(ila)?)?",
        "/oct(ober)?/": "urr(.(ia)?)?",
        "/nov(ember)?/": "aza(.(roa)?)?",
        "/dec(ember)?/": "abe(.(ndua)?)?",
        "/^su(n(day)?)?/": "^ig((.(andea)?)?)?",
        "/^mo(n(day)?)?/": "^al((.(telehena)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^as((.(teartea)?)?)?",
        "/^we(d(nesday)?)?/": "^az((.(teazkena)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^og((.(teguna)?)?)?",
        "/^fr(i(day)?)?/": "^or((.(tirala)?)?)?",
        "/^sa(t(urday)?)?/": "^lr((.(runbata)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "eu-ES";
