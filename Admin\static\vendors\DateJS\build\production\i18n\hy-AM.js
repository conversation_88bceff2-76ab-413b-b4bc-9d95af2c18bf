/* 
 * DateJS Culture String File
 * Country Code: hy-AM
 * Name: Armenian (Armenia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["hy-AM"] = {
        "name": "hy-AM",
        "englishName": "Armenian (Armenia)",
        "nativeName": "Հայերեն (Հայաստան)",
        "Sunday": "Կիրակի",
        "Monday": "Երկուշաբթի",
        "Tuesday": "Երեքշաբթի",
        "Wednesday": "Չորեքշաբթի",
        "Thursday": "Հինգշաբթի",
        "Friday": "ՈՒրբաթ",
        "Saturday": "Շաբաթ",
        "Sun": "Կիր",
        "Mon": "Երկ",
        "Tue": "Երք",
        "Wed": "Չրք",
        "Thu": "Հնգ",
        "Fri": "ՈՒր",
        "Sat": "Շբթ",
        "Su": "Կ",
        "Mo": "Ե",
        "Tu": "Ե",
        "We": "Չ",
        "Th": "Հ",
        "Fr": "Ո",
        "Sa": "Շ",
        "S_Sun_Initial": "Կ",
        "M_Mon_Initial": "Ե",
        "T_Tue_Initial": "Ե",
        "W_Wed_Initial": "Չ",
        "T_Thu_Initial": "Հ",
        "F_Fri_Initial": "Ո",
        "S_Sat_Initial": "Շ",
        "January": "Հունվար",
        "February": "Փետրվար",
        "March": "Մարտ",
        "April": "Ապրիլ",
        "May": "Մայիս",
        "June": "Հունիս",
        "July": "Հուլիս",
        "August": "Օգոստոս",
        "September": "Սեպտեմբեր",
        "October": "Հոկտեմբեր",
        "November": "Նոյեմբեր",
        "December": "Դեկտեմբեր",
        "Jan_Abbr": "ՀՆՎ",
        "Feb_Abbr": "ՓՏՎ",
        "Mar_Abbr": "ՄՐՏ",
        "Apr_Abbr": "ԱՊՐ",
        "May_Abbr": "ՄՅՍ",
        "Jun_Abbr": "ՀՆՍ",
        "Jul_Abbr": "ՀԼՍ",
        "Aug_Abbr": "ՕԳՍ",
        "Sep_Abbr": "ՍԵՊ",
        "Oct_Abbr": "ՀՈԿ",
        "Nov_Abbr": "ՆՈՅ",
        "Dec_Abbr": "ԴԵԿ",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM, yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM, yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "հունվար",
        "/feb(ruary)?/": "փետրվար",
        "/mar(ch)?/": "մարտ",
        "/apr(il)?/": "ապր(իլ)?",
        "/may/": "մայիս",
        "/jun(e)?/": "հունիս",
        "/jul(y)?/": "հուլիս",
        "/aug(ust)?/": "օգոստոս",
        "/sep(t(ember)?)?/": "սեպ(տեմբեր)?",
        "/oct(ober)?/": "հոկ(տեմբեր)?",
        "/nov(ember)?/": "նոյ(եմբեր)?",
        "/dec(ember)?/": "դեկ(տեմբեր)?",
        "/^su(n(day)?)?/": "^կ(իր(ակի)?)?",
        "/^mo(n(day)?)?/": "^ե(րկ(ուշաբթի)?)?",
        "/^tu(e(s(day)?)?)?/": "^ե(րք(քշաբթի)?)?",
        "/^we(d(nesday)?)?/": "^չ(րք(եքշաբթի)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^հ(նգ(գշաբթի)?)?",
        "/^fr(i(day)?)?/": "^ո(ւր(բաթ)?)?",
        "/^sa(t(urday)?)?/": "^շ(բթ(աթ)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "hy-AM";
