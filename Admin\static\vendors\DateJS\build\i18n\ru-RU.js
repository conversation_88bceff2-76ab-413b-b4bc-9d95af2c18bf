/* 
 * DateJS Culture String File
 * Country Code: ru-RU
 * Name: Russian (Russia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ru-RU"] = {
        "name": "ru-RU",
        "englishName": "Russian (Russia)",
        "nativeName": "Pусский (Россия)",
        "Sunday": "воскресенье",
        "Monday": "понедельник",
        "Tuesday": "вторник",
        "Wednesday": "среда",
        "Thursday": "четверг",
        "Friday": "пятница",
        "Saturday": "суббота",
        "Sun": "Вс",
        "Mon": "Пн",
        "Tue": "Вт",
        "Wed": "Ср",
        "Thu": "Чт",
        "Fri": "Пт",
        "Sat": "Сб",
        "Su": "Вс",
        "Mo": "Пн",
        "Tu": "Вт",
        "We": "Ср",
        "Th": "Чт",
        "Fr": "Пт",
        "Sa": "Сб",
        "S_Sun_Initial": "В",
        "M_Mon_Initial": "П",
        "T_Tue_Initial": "В",
        "W_Wed_Initial": "С",
        "T_Thu_Initial": "Ч",
        "F_Fri_Initial": "П",
        "S_Sat_Initial": "С",
        "January": "Январь",
        "February": "Февраль",
        "March": "Март",
        "April": "Апрель",
        "May": "Май",
        "June": "Июнь",
        "July": "Июль",
        "August": "Август",
        "September": "Сентябрь",
        "October": "Октябрь",
        "November": "Ноябрь",
        "December": "Декабрь",
        "Jan_Abbr": "янв",
        "Feb_Abbr": "фев",
        "Mar_Abbr": "мар",
        "Apr_Abbr": "апр",
        "May_Abbr": "май",
        "Jun_Abbr": "июн",
        "Jul_Abbr": "июл",
        "Aug_Abbr": "авг",
        "Sep_Abbr": "сен",
        "Oct_Abbr": "окт",
        "Nov_Abbr": "ноя",
        "Dec_Abbr": "дек",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy 'г.'",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy 'г.' H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy 'г.'",
        "/jan(uary)?/": "янв(арь)?",
        "/feb(ruary)?/": "фев(раль)?",
        "/mar(ch)?/": "мар(т)?",
        "/apr(il)?/": "апр(ель)?",
        "/may/": "май",
        "/jun(e)?/": "июн(ь)?",
        "/jul(y)?/": "июл(ь)?",
        "/aug(ust)?/": "авг(уст)?",
        "/sep(t(ember)?)?/": "сен(тябрь)?",
        "/oct(ober)?/": "окт(ябрь)?",
        "/nov(ember)?/": "ноя(брь)?",
        "/dec(ember)?/": "дек(абрь)?",
        "/^su(n(day)?)?/": "^воскресенье",
        "/^mo(n(day)?)?/": "^понедельник",
        "/^tu(e(s(day)?)?)?/": "^вторник",
        "/^we(d(nesday)?)?/": "^среда",
        "/^th(u(r(s(day)?)?)?)?/": "^четверг",
        "/^fr(i(day)?)?/": "^пятница",
        "/^sa(t(urday)?)?/": "^суббота",
        "/^next/": "^след|завтра",
        "/^last|past|prev(ious)?/": "^пред|вчера",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|через|после|вперед|и|следую?щ(ая|ий|ее)?)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|за|до|поза|пе?ред((ыдущ|шев?ствующ)(ая|ий|ее))|назад)",
        "/^yes(terday)?/": "^вчера",
        "/^t(od(ay)?)?/": "^сегодня",
        "/^tom(orrow)?/": "^завтра",
        "/^n(ow)?/": "^сейчас|сечас|щас",
        "/^ms|milli(second)?s?/": "^мс|мили(секунд)?s?",
        "/^sec(ond)?s?/": "^с(ек(унд)?)?",
        "/^mn|min(ute)?s?/": "^м(ин(ут)?)?",
        "/^h(our)?s?/": "^ч((ас)?ов)?",
        "/^w(eek)?s?/": "^н(ед(ель)?)?",
        "/^m(onth)?s?/": "^мес(яцев)?",
        "/^d(ay)?s?/": "^д(ень|ней|ня)?",
        "/^y(ear)?s?/": "^г(ода?)?|л(ет)?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ru-RU";
