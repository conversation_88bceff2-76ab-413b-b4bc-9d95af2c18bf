/* 
 * DateJS Culture String File
 * Country Code: cy-GB
 * Name: Welsh (United Kingdom)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["cy-GB"] = {
        "name": "cy-GB",
        "englishName": "Welsh (United Kingdom)",
        "nativeName": "<PERSON><PERSON>raeg (y Deyrnas Unedig)",
        "Sunday": "Dydd Sul",
        "Monday": "Dydd Llun",
        "Tuesday": "Dydd Mawrth",
        "Wednesday": "Dydd Mercher",
        "Thursday": "Dydd Iau",
        "Friday": "<PERSON>ydd Gwener",
        "Saturday": "Dydd Sadwrn",
        "Sun": "Sul",
        "Mon": "Llun",
        "Tue": "Maw",
        "Wed": "Mer",
        "Thu": "Iau",
        "Fri": "Gwe",
        "Sat": "Sad",
        "Su": "Sul",
        "<PERSON>": "<PERSON>lu<PERSON>",
        "Tu": "Maw",
        "We": "Mer",
        "Th": "<PERSON><PERSON>",
        "Fr": "Gwe",
        "Sa": "Sad",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "L",
        "T_Tue_Initial": "M",
        "W_Wed_Initial": "M",
        "T_Thu_Initial": "I",
        "F_Fri_Initial": "G",
        "S_Sat_Initial": "S",
        "January": "Ionawr",
        "February": "Chwefror",
        "March": "Mawrth",
        "April": "Ebrill",
        "May": "Mai",
        "June": "Mehefin",
        "July": "Gorffennaf",
        "August": "Awst",
        "September": "Medi",
        "October": "Hydref",
        "November": "Tachwedd",
        "December": "Rhagfyr",
        "Jan_Abbr": "Ion",
        "Feb_Abbr": "Chwe",
        "Mar_Abbr": "Maw",
        "Apr_Abbr": "Ebr",
        "May_Abbr": "Mai",
        "Jun_Abbr": "Meh",
        "Jul_Abbr": "Gor",
        "Aug_Abbr": "Aws",
        "Sep_Abbr": "Med",
        "Oct_Abbr": "Hyd",
        "Nov_Abbr": "Tach",
        "Dec_Abbr": "Rhag",
        "AM": "a.m.",
        "PM": "p.m.",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm:ss",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ion(awr)?",
        "/feb(ruary)?/": "chwe(fror)?",
        "/mar(ch)?/": "maw(rth)?",
        "/apr(il)?/": "ebr(ill)?",
        "/may/": "mai",
        "/jun(e)?/": "meh(efin)?",
        "/jul(y)?/": "gor(ffennaf)?",
        "/aug(ust)?/": "aws(t)?",
        "/sep(t(ember)?)?/": "med(i)?",
        "/oct(ober)?/": "hyd(ref)?",
        "/nov(ember)?/": "tach(wedd)?",
        "/dec(ember)?/": "rhag(fyr)?",
        "/^su(n(day)?)?/": "^dydd sul",
        "/^mo(n(day)?)?/": "^dydd llun",
        "/^tu(e(s(day)?)?)?/": "^dydd mawrth",
        "/^we(d(nesday)?)?/": "^dydd mercher",
        "/^th(u(r(s(day)?)?)?)?/": "^dydd iau",
        "/^fr(i(day)?)?/": "^dydd gwener",
        "/^sa(t(urday)?)?/": "^dydd sadwrn",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "cy-GB";
