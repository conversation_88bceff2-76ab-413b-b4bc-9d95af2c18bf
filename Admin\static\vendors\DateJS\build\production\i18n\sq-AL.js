/* 
 * DateJS Culture String File
 * Country Code: sq-AL
 * Name: Albanian (Albania)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sq-AL"] = {
        "name": "sq-AL",
        "englishName": "Albanian (Albania)",
        "nativeName": "shqipe (Shqipëria)",
        "Sunday": "e diel",
        "Monday": "e hënë",
        "Tuesday": "e martë",
        "Wednesday": "e mërkurë",
        "Thursday": "e enjte",
        "Friday": "e premte",
        "Saturday": "e shtunë",
        "Sun": "Die",
        "Mon": "Hën",
        "Tue": "Mar",
        "Wed": "Mër",
        "Thu": "Enj",
        "Fri": "Pre",
        "Sat": "Sht",
        "Su": "Di",
        "Mo": "Hë",
        "Tu": "Ma",
        "We": "<PERSON><PERSON>",
        "Th": "En",
        "Fr": "Pr",
        "Sa": "Sh",
        "S_Sun_Initial": "D",
        "M_Mon_Initial": "H",
        "T_Tue_Initial": "M",
        "W_Wed_Initial": "M",
        "T_Thu_Initial": "E",
        "F_Fri_Initial": "P",
        "S_Sat_Initial": "S",
        "January": "janar",
        "February": "shkurt",
        "March": "mars",
        "April": "prill",
        "May": "maj",
        "June": "qershor",
        "July": "korrik",
        "August": "gusht",
        "September": "shtator",
        "October": "tetor",
        "November": "nëntor",
        "December": "dhjetor",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Shk",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Pri",
        "May_Abbr": "Maj",
        "Jun_Abbr": "Qer",
        "Jul_Abbr": "Kor",
        "Aug_Abbr": "Gsh",
        "Sep_Abbr": "Sht",
        "Oct_Abbr": "Tet",
        "Nov_Abbr": "Nën",
        "Dec_Abbr": "Dhj",
        "AM": "PD",
        "PM": "MD",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy-MM-dd",
        "dddd, MMMM dd, yyyy": "yyyy-MM-dd",
        "h:mm tt": "h:mm.tt",
        "h:mm:ss tt": "h:mm:ss.tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy-MM-dd h:mm:ss.tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "yyyy-MM",
        "/jan(uary)?/": "jan(ar)?",
        "/feb(ruary)?/": "shk(urt)?",
        "/mar(ch)?/": "mar(s)?",
        "/apr(il)?/": "pri(ll)?",
        "/may/": "maj",
        "/jun(e)?/": "qer(shor)?",
        "/jul(y)?/": "kor(rik)?",
        "/aug(ust)?/": "gusht",
        "/sep(t(ember)?)?/": "sht(ator)?",
        "/oct(ober)?/": "tet(or)?",
        "/nov(ember)?/": "nën(tor)?",
        "/dec(ember)?/": "dhj(etor)?",
        "/^su(n(day)?)?/": "^di(e(iel)?)?",
        "/^mo(n(day)?)?/": "^hë(n(ënë)?)?",
        "/^tu(e(s(day)?)?)?/": "^ma(r(artë)?)?",
        "/^we(d(nesday)?)?/": "^më(r(ërkurë)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^en(j(njte)?)?",
        "/^fr(i(day)?)?/": "^pr(e(remte)?)?",
        "/^sa(t(urday)?)?/": "^sh(t(htunë)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sq-AL";
