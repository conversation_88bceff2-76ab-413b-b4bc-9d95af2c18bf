{"name": "autosize", "description": "Autosize is a small, stand-alone script to automatically adjust textarea height to fit text.", "version": "3.0.15", "keywords": ["textarea", "form", "ui"], "files": ["dist", "src"], "author": {"name": "<PERSON>", "url": "http://www.jacklmoore.com", "email": "<EMAIL>"}, "main": "dist/autosize.js", "license": "MIT", "homepage": "http://www.jacklmoore.com/autosize", "demo": "http://www.jacklmoore.com/autosize", "repository": {"type": "git", "url": "http://github.com/jackmoore/autosize.git"}, "dependencies": {}, "devDependencies": {"babel": "^5.4.3", "gaze": "^0.5.1", "jshint": "^2.5.6", "uglify-js": "^2.4.22"}, "config": {"bower": {"name": "autosize", "ignore": [], "moduleType": ["amd", "node"]}, "title": "Autosize", "filename": "autosize"}, "scripts": {"build": "node build"}}