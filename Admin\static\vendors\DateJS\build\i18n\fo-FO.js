/* 
 * DateJS Culture String File
 * Country Code: fo-FO
 * Name: Faroese (Faroe Islands)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["fo-FO"] = {
        "name": "fo-FO",
        "englishName": "Faroese (Faroe Islands)",
        "nativeName": "føroyskt (Føroyar)",
        "Sunday": "sunnudagur",
        "Monday": "mánadagur",
        "Tuesday": "týsdagur",
        "Wednesday": "mikudagur",
        "Thursday": "hósdagur",
        "Friday": "fríggjadagur",
        "Saturday": "leygardagur",
        "Sun": "sun",
        "Mon": "mán",
        "Tue": "týs",
        "Wed": "mik",
        "Thu": "hós",
        "Fri": "frí",
        "Sat": "leyg",
        "Su": "su",
        "Mo": "má",
        "Tu": "tý",
        "We": "mi",
        "Th": "hó",
        "Fr": "fr",
        "Sa": "ley",
        "S_Sun_Initial": "s",
        "M_Mon_Initial": "m",
        "T_Tue_Initial": "t",
        "W_Wed_Initial": "m",
        "T_Thu_Initial": "h",
        "F_Fri_Initial": "f",
        "S_Sat_Initial": "l",
        "January": "januar",
        "February": "februar",
        "March": "mars",
        "April": "apríl",
        "May": "mai",
        "June": "juni",
        "July": "juli",
        "August": "august",
        "September": "september",
        "October": "oktober",
        "November": "november",
        "December": "desember",
        "Jan_Abbr": "jan",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "apr",
        "May_Abbr": "mai",
        "Jun_Abbr": "jun",
        "Jul_Abbr": "jul",
        "Aug_Abbr": "aug",
        "Sep_Abbr": "sep",
        "Oct_Abbr": "okt",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "des",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "HH.mm",
        "h:mm:ss tt": "HH.mm.ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy HH.mm.ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(uar)?",
        "/feb(ruary)?/": "feb(ruar)?",
        "/mar(ch)?/": "mar(s)?",
        "/apr(il)?/": "apr(íl)?",
        "/may/": "mai",
        "/jun(e)?/": "jun(i)?",
        "/jul(y)?/": "jul(i)?",
        "/aug(ust)?/": "aug(ust)?",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "okt(ober)?",
        "/nov(ember)?/": "nov(ember)?",
        "/dec(ember)?/": "des(ember)?",
        "/^su(n(day)?)?/": "^su(n(nudagur)?)?",
        "/^mo(n(day)?)?/": "^má(n(adagur)?)?",
        "/^tu(e(s(day)?)?)?/": "^tý(s(dagur)?)?",
        "/^we(d(nesday)?)?/": "^mi(k(udagur)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^hó(s(dagur)?)?",
        "/^fr(i(day)?)?/": "^fr(í(ggjadagur)?)?",
        "/^sa(t(urday)?)?/": "^ley(g(ardagur)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "fo-FO";
