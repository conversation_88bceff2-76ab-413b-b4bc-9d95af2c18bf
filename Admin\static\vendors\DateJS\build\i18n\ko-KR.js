/* 
 * DateJS Culture String File
 * Country Code: ko-KR
 * Name: Korean (Korea)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ko-KR"] = {
        "name": "ko-KR",
        "englishName": "Korean (Korea)",
        "nativeName": "한국어 (대한민국)",
        "Sunday": "일요일",
        "Monday": "월요일",
        "Tuesday": "화요일",
        "Wednesday": "수요일",
        "Thursday": "목요일",
        "Friday": "금요일",
        "Saturday": "토요일",
        "Sun": "일",
        "Mon": "월",
        "Tue": "화",
        "Wed": "수",
        "Thu": "목",
        "Fri": "금",
        "Sat": "토",
        "Su": "일",
        "Mo": "월",
        "Tu": "화",
        "We": "수",
        "Th": "목",
        "Fr": "금",
        "Sa": "토",
        "S_Sun_Initial": "일",
        "M_Mon_Initial": "월",
        "T_Tue_Initial": "화",
        "W_Wed_Initial": "수",
        "T_Thu_Initial": "목",
        "F_Fri_Initial": "금",
        "S_Sat_Initial": "토",
        "January": "1월",
        "February": "2월",
        "March": "3월",
        "April": "4월",
        "May": "5월",
        "June": "6월",
        "July": "7월",
        "August": "8월",
        "September": "9월",
        "October": "10월",
        "November": "11월",
        "December": "12월",
        "Jan_Abbr": "1",
        "Feb_Abbr": "2",
        "Mar_Abbr": "3",
        "Apr_Abbr": "4",
        "May_Abbr": "5",
        "Jun_Abbr": "6",
        "Jul_Abbr": "7",
        "Aug_Abbr": "8",
        "Sep_Abbr": "9",
        "Oct_Abbr": "10",
        "Nov_Abbr": "11",
        "Dec_Abbr": "12",
        "AM": "오전",
        "PM": "오후",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy-MM-dd",
        "dddd, MMMM dd, yyyy": "yyyy'년' M'월' d'일' dddd",
        "h:mm tt": "tt h:mm",
        "h:mm:ss tt": "tt h:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy'년' M'월' d'일' dddd tt h:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "M'월' d'일'",
        "MMMM, yyyy": "yyyy'년' M'월'",
        "/jan(uary)?/": "1(월)?",
        "/feb(ruary)?/": "2(월)?",
        "/mar(ch)?/": "3(월)?",
        "/apr(il)?/": "4(월)?",
        "/may/": "5(월)?",
        "/jun(e)?/": "6(월)?",
        "/jul(y)?/": "7(월)?",
        "/aug(ust)?/": "8(월)?",
        "/sep(t(ember)?)?/": "9(월)?",
        "/oct(ober)?/": "10(월)?",
        "/nov(ember)?/": "11(월)?",
        "/dec(ember)?/": "12(월)?",
        "/^su(n(day)?)?/": "^일요일",
        "/^mo(n(day)?)?/": "^월요일",
        "/^tu(e(s(day)?)?)?/": "^화요일",
        "/^we(d(nesday)?)?/": "^수요일",
        "/^th(u(r(s(day)?)?)?)?/": "^목요일",
        "/^fr(i(day)?)?/": "^금요일",
        "/^sa(t(urday)?)?/": "^토요일",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ko-KR";
