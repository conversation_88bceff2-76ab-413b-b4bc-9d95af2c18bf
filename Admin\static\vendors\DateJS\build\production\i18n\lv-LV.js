/* 
 * DateJS Culture String File
 * Country Code: lv-LV
 * Name: Latvian (Latvia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["lv-LV"] = {
        "name": "lv-LV",
        "englishName": "Latvian (Latvia)",
        "nativeName": "latviešu (Latvija)",
        "Sunday": "svētdiena",
        "Monday": "pirmdiena",
        "Tuesday": "otrdiena",
        "Wednesday": "trešdiena",
        "Thursday": "ceturtdiena",
        "Friday": "piektdiena",
        "Saturday": "sestdiena",
        "Sun": "Sv",
        "Mon": "Pr",
        "Tue": "Ot",
        "Wed": "Tr",
        "Thu": "Ce",
        "Fri": "Pk",
        "Sat": "Se",
        "Su": "Sv",
        "Mo": "Pr",
        "Tu": "Ot",
        "We": "Tr",
        "Th": "Ce",
        "Fr": "Pk",
        "Sa": "Se",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "P",
        "T_Tue_Initial": "O",
        "W_Wed_Initial": "T",
        "T_Thu_Initial": "C",
        "F_Fri_Initial": "P",
        "S_Sat_Initial": "S",
        "January": "janvāris",
        "February": "februāris",
        "March": "marts",
        "April": "aprīlis",
        "May": "maijs",
        "June": "jūnijs",
        "July": "jūlijs",
        "August": "augusts",
        "September": "septembris",
        "October": "oktobris",
        "November": "novembris",
        "December": "decembris",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Feb",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Apr",
        "May_Abbr": "Mai",
        "Jun_Abbr": "Jūn",
        "Jul_Abbr": "Jūl",
        "Aug_Abbr": "Aug",
        "Sep_Abbr": "Sep",
        "Oct_Abbr": "Okt",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Dec",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy.MM.dd.",
        "dddd, MMMM dd, yyyy": "dddd, yyyy'. gada 'd. MMMM",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, yyyy'. gada 'd. MMMM H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "yyyy. MMMM",
        "/jan(uary)?/": "jan(vāris)?",
        "/feb(ruary)?/": "feb(ruāris)?",
        "/mar(ch)?/": "mar(ts)?",
        "/apr(il)?/": "apr(īlis)?",
        "/may/": "mai(js)?",
        "/jun(e)?/": "jūn(ijs)?",
        "/jul(y)?/": "jūl(ijs)?",
        "/aug(ust)?/": "aug(usts)?",
        "/sep(t(ember)?)?/": "sep(tembris)?",
        "/oct(ober)?/": "okt(obris)?",
        "/nov(ember)?/": "nov(embris)?",
        "/dec(ember)?/": "dec(embris)?",
        "/^su(n(day)?)?/": "^svētdiena",
        "/^mo(n(day)?)?/": "^pirmdiena",
        "/^tu(e(s(day)?)?)?/": "^otrdiena",
        "/^we(d(nesday)?)?/": "^trešdiena",
        "/^th(u(r(s(day)?)?)?)?/": "^ceturtdiena",
        "/^fr(i(day)?)?/": "^piektdiena",
        "/^sa(t(urday)?)?/": "^sestdiena",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "lv-LV";
