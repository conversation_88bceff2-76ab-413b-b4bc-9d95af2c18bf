/* 
 * DateJS Culture String File
 * Country Code: vi-VN
 * Name: Vietnamese (Vietnam)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["vi-VN"] = {
        "name": "vi-VN",
        "englishName": "Vietnamese (Vietnam)",
        "nativeName": "Tiếng Việt (Việt Nam)",
        "Sunday": "Chủ Nhật",
        "Monday": "Thứ Hai",
        "Tuesday": "Thứ Ba",
        "Wednesday": "Thứ Tư",
        "Thursday": "Thứ Năm",
        "Friday": "Thư<PERSON> Sáu",
        "Saturday": "Thứ Bảy",
        "Sun": "CN",
        "Mon": "Hai",
        "Tue": "Ba",
        "Wed": "Tư",
        "Thu": "Năm",
        "Fri": "Sáu",
        "Sat": "Bảy",
        "Su": "C",
        "Mo": "H",
        "Tu": "B",
        "We": "T",
        "Th": "N",
        "Fr": "S",
        "Sa": "B",
        "S_Sun_Initial": "C",
        "M_Mon_Initial": "H",
        "T_Tue_Initial": "B",
        "W_Wed_Initial": "T",
        "T_Thu_Initial": "N",
        "F_Fri_Initial": "S",
        "S_Sat_Initial": "B",
        "January": "Tháng Giêng",
        "February": "Tháng Hai",
        "March": "Tháng Ba",
        "April": "Tháng Tư",
        "May": "Tháng Năm",
        "June": "Tháng Sáu",
        "July": "Tháng Bảy",
        "August": "Tháng Tám",
        "September": "Tháng Chín",
        "October": "Tháng Mười",
        "November": "Tháng Mười Một",
        "December": "Tháng Mười Hai",
        "Jan_Abbr": "Thg1",
        "Feb_Abbr": "Thg2",
        "Mar_Abbr": "Thg3",
        "Apr_Abbr": "Thg4",
        "May_Abbr": "Thg5",
        "Jun_Abbr": "Thg6",
        "Jul_Abbr": "Thg7",
        "Aug_Abbr": "Thg8",
        "Sep_Abbr": "Thg9",
        "Oct_Abbr": "Thg10",
        "Nov_Abbr": "Thg11",
        "Dec_Abbr": "Thg12",
        "AM": "SA",
        "PM": "CH",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "h:mm tt",
        "h:mm:ss tt": "h:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy h:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "tháng giêng",
        "/feb(ruary)?/": "tháng hai",
        "/mar(ch)?/": "tháng ba",
        "/apr(il)?/": "tháng tư",
        "/may/": "tháng năm",
        "/jun(e)?/": "tháng sáu",
        "/jul(y)?/": "tháng bảy",
        "/aug(ust)?/": "tháng tám",
        "/sep(t(ember)?)?/": "tháng chín",
        "/oct(ober)?/": "tháng mười",
        "/nov(ember)?/": "tháng mười một",
        "/dec(ember)?/": "tháng mười hai",
        "/^su(n(day)?)?/": "^c(n(ủ nhật)?)?",
        "/^mo(n(day)?)?/": "^h(ai(́ hai)?)?",
        "/^tu(e(s(day)?)?)?/": "^b(a(ứ ba)?)?",
        "/^we(d(nesday)?)?/": "^t(ư(ứ tư)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^n(ăm(́ năm)?)?",
        "/^fr(i(day)?)?/": "^s(áu( sáu)?)?",
        "/^sa(t(urday)?)?/": "^b(ảy( bảy)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "vi-VN";
