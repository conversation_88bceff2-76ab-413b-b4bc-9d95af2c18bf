/* 
 * DateJS Culture String File
 * Country Code: kn-IN
 * Name: Kannada (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["kn-IN"] = {
        "name": "kn-IN",
        "englishName": "Kannada (India)",
        "nativeName": "ಕನ್ನಡ (ಭಾರತ)",
        "Sunday": "ಭಾನುವಾರ",
        "Monday": "ಸೋಮವಾರ",
        "Tuesday": "ಮಂಗಳವಾರ",
        "Wednesday": "ಬುಧವಾರ",
        "Thursday": "ಗುರುವಾರ",
        "Friday": "ಶುಕ್ರವಾರ",
        "Saturday": "ಶನಿವಾರ",
        "Sun": "ಭಾನು.",
        "Mon": "ಸೋಮ.",
        "Tue": "ಮಂಗಳ.",
        "Wed": "ಬುಧ.",
        "Thu": "ಗುರು.",
        "Fri": "ಶುಕ್ರ.",
        "Sat": "ಶನಿ.",
        "Su": "ರ",
        "Mo": "ಸ",
        "Tu": "ಮ",
        "We": "ಬ",
        "Th": "ಗ",
        "Fr": "ಶ",
        "Sa": "ಶ",
        "S_Sun_Initial": "ರ",
        "M_Mon_Initial": "ಸ",
        "T_Tue_Initial": "ಮ",
        "W_Wed_Initial": "ಬ",
        "T_Thu_Initial": "ಗ",
        "F_Fri_Initial": "ಶ",
        "S_Sat_Initial": "ಶ",
        "January": "ಜನವರಿ",
        "February": "ಫೆಬ್ರವರಿ",
        "March": "ಮಾರ್ಚ್",
        "April": "ಎಪ್ರಿಲ್",
        "May": "ಮೇ",
        "June": "ಜೂನ್",
        "July": "ಜುಲೈ",
        "August": "ಆಗಸ್ಟ್",
        "September": "ಸೆಪ್ಟಂಬರ್",
        "October": "ಅಕ್ಟೋಬರ್",
        "November": "ನವೆಂಬರ್",
        "December": "ಡಿಸೆಂಬರ್",
        "Jan_Abbr": "ಜನವರಿ",
        "Feb_Abbr": "ಫೆಬ್ರವರಿ",
        "Mar_Abbr": "ಮಾರ್ಚ್",
        "Apr_Abbr": "ಎಪ್ರಿಲ್",
        "May_Abbr": "ಮೇ",
        "Jun_Abbr": "ಜೂನ್",
        "Jul_Abbr": "ಜುಲೈ",
        "Aug_Abbr": "ಆಗಸ್ಟ್",
        "Sep_Abbr": "ಸೆಪ್ಟಂಬರ್",
        "Oct_Abbr": "ಅಕ್ಟೋಬರ್",
        "Nov_Abbr": "ನವೆಂಬರ್",
        "Dec_Abbr": "ಡಿಸೆಂಬರ್",
        "AM": "ಪೂರ್ವಾಹ್ನ",
        "PM": "ಅಪರಾಹ್ನ",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "ಜನವರಿ",
        "/feb(ruary)?/": "ಫೆಬ್ರವರಿ",
        "/mar(ch)?/": "ಮಾರ್ಚ್",
        "/apr(il)?/": "ಎಪ್ರಿಲ್",
        "/may/": "ಮೇ",
        "/jun(e)?/": "ಜೂನ್",
        "/jul(y)?/": "ಜುಲೈ",
        "/aug(ust)?/": "ಆಗಸ್ಟ್",
        "/sep(t(ember)?)?/": "ಸೆಪ್ಟಂಬರ್",
        "/oct(ober)?/": "ಅಕ್ಟೋಬರ್",
        "/nov(ember)?/": "ನವೆಂಬರ್",
        "/dec(ember)?/": "ಡಿಸೆಂಬರ್",
        "/^su(n(day)?)?/": "^ರ(ಾನು(.(ವಾರ)?)?)?",
        "/^mo(n(day)?)?/": "^ಸ(ೋಮ(.(ವಾರ)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^ಮ(ಂಗಳ(.(ವಾರ)?)?)?",
        "/^we(d(nesday)?)?/": "^ಬ(ುಧ(.(ವಾರ)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ಗ(ುರು(.(ವಾರ)?)?)?",
        "/^fr(i(day)?)?/": "^ಶ(ುಕ್ರ(.(ವಾರ)?)?)?",
        "/^sa(t(urday)?)?/": "^ಶ(ನಿ(.(ವಾರ)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "kn-IN";
