/* 
 * DateJS Culture String File
 * Country Code: sms-FI
 * Name: <PERSON> (Skolt) (Finland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sms-FI"] = {
        "name": "sms-FI",
        "englishName": "<PERSON> (Skolt) (Finland)",
        "nativeName": "sääm´ǩiõll (Lää´ddjânnam)",
        "Sunday": "pâ´sspei´vv",
        "Monday": "vuõssargg",
        "Tuesday": "mââibargg",
        "Wednesday": "seärad",
        "Thursday": "nelljdpei´vv",
        "Friday": "piâtnâc",
        "Saturday": "sue´vet",
        "Sun": "pâ",
        "Mon": "vu",
        "Tue": "mâ",
        "Wed": "se",
        "Thu": "ne",
        "Fri": "pi",
        "Sat": "su",
        "Su": "pâ",
        "Mo": "vu",
        "Tu": "mâ",
        "We": "se",
        "Th": "ne",
        "Fr": "pi",
        "Sa": "su",
        "S_Sun_Initial": "p",
        "M_Mon_Initial": "v",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "s",
        "T_Thu_Initial": "n",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "s",
        "January": "ođđee´jjmään",
        "February": "tä´lvvmään",
        "March": "pâ´zzlâšttammään",
        "April": "njuhččmään",
        "May": "vue´ssmään",
        "June": "ǩie´ssmään",
        "July": "suei´nnmään",
        "August": "på´rǧǧmään",
        "September": "čõhččmään",
        "October": "kålggmään",
        "November": "skamm´mään",
        "December": "rosttovmään",
        "Jan_Abbr": "ođjm",
        "Feb_Abbr": "tä´lvv",
        "Mar_Abbr": "pâzl",
        "Apr_Abbr": "njuh",
        "May_Abbr": "vue",
        "Jun_Abbr": "ǩie",
        "Jul_Abbr": "suei",
        "Aug_Abbr": "på´r",
        "Sep_Abbr": "čõh",
        "Oct_Abbr": "kålg",
        "Nov_Abbr": "ska",
        "Dec_Abbr": "rost",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "MMMM d'. p. 'yyyy",
        "h:mm tt": "H:mm:ss",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "MMMM d'. p. 'yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ođđee´jjmään",
        "/feb(ruary)?/": "tä´lvv(mään)?",
        "/mar(ch)?/": "pâ´zzlâšttammään",
        "/apr(il)?/": "njuh(ččmään)?",
        "/may/": "vue(´ssmään)?",
        "/jun(e)?/": "ǩie(´ssmään)?",
        "/jul(y)?/": "suei(´nnmään)?",
        "/aug(ust)?/": "på´r(ǧǧmään)?",
        "/sep(t(ember)?)?/": "čõh(ččmään)?",
        "/oct(ober)?/": "kålg(gmään)?",
        "/nov(ember)?/": "ska(mm´mään)?",
        "/dec(ember)?/": "rost(tovmään)?",
        "/^su(n(day)?)?/": "^pâ´sspei´vv",
        "/^mo(n(day)?)?/": "^vuõssargg",
        "/^tu(e(s(day)?)?)?/": "^mââibargg",
        "/^we(d(nesday)?)?/": "^seärad",
        "/^th(u(r(s(day)?)?)?)?/": "^nelljdpei´vv",
        "/^fr(i(day)?)?/": "^piâtnâc",
        "/^sa(t(urday)?)?/": "^sue´vet",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sms-FI";
