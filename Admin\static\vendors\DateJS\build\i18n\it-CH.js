/* 
 * DateJS Culture String File
 * Country Code: it-CH
 * Name: Italian (Switzerland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["it-CH"] = {
        "name": "it-CH",
        "englishName": "Italian (Switzerland)",
        "nativeName": "italiano (Svizzera)",
        "Sunday": "domenica",
        "Monday": "lunedì",
        "Tuesday": "martedì",
        "Wednesday": "mercoledì",
        "Thursday": "giovedì",
        "Friday": "venerdì",
        "Saturday": "sabato",
        "Sun": "dom",
        "Mon": "lun",
        "Tue": "mar",
        "Wed": "mer",
        "Thu": "gio",
        "Fri": "ven",
        "Sat": "sab",
        "Su": "do",
        "Mo": "lu",
        "Tu": "ma",
        "We": "me",
        "Th": "gi",
        "Fr": "ve",
        "Sa": "sa",
        "S_Sun_Initial": "d",
        "M_Mon_Initial": "l",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "m",
        "T_Thu_Initial": "g",
        "F_Fri_Initial": "v",
        "S_Sat_Initial": "s",
        "January": "gennaio",
        "February": "febbraio",
        "March": "marzo",
        "April": "aprile",
        "May": "maggio",
        "June": "giugno",
        "July": "luglio",
        "August": "agosto",
        "September": "settembre",
        "October": "ottobre",
        "November": "novembre",
        "December": "dicembre",
        "Jan_Abbr": "gen",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "apr",
        "May_Abbr": "mag",
        "Jun_Abbr": "gio",
        "Jul_Abbr": "lug",
        "Aug_Abbr": "ago",
        "Sep_Abbr": "set",
        "Oct_Abbr": "ott",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dic",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d. MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d. MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "gen(naio)?",
        "/feb(ruary)?/": "feb(braio)?",
        "/mar(ch)?/": "mar(zo)?",
        "/apr(il)?/": "apr(ile)?",
        "/may/": "mag(gio)?",
        "/jun(e)?/": "giugno",
        "/jul(y)?/": "lug(lio)?",
        "/aug(ust)?/": "ago(sto)?",
        "/sep(t(ember)?)?/": "set(tembre)?",
        "/oct(ober)?/": "ott(obre)?",
        "/nov(ember)?/": "nov(embre)?",
        "/dec(ember)?/": "dic(embre)?",
        "/^su(n(day)?)?/": "^do(m(enica)?)?",
        "/^mo(n(day)?)?/": "^lu(n(edì)?)?",
        "/^tu(e(s(day)?)?)?/": "^ma(r(tedì)?)?",
        "/^we(d(nesday)?)?/": "^me(r(coledì)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^gi(o(vedì)?)?",
        "/^fr(i(day)?)?/": "^ve(n(erdì)?)?",
        "/^sa(t(urday)?)?/": "^sa(b(ato)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "it-CH";
