# Changelog

## 0.9.0 (2015-05-15)

* Fix #49 runtime option override support
* Fix #48 extend bower.json

## 0.8.5 (2015-04-04)

* Add bootstrap 3.3.1-3.3.4 styles
* Add SCSS support (request #46)
* Fix #42 wrong version name

## 0.8.4 (2014-11-05)

* Add bootstrap 3.3.0 styles

## 0.8.3 (2014-08-08)

* Fix #39 revert low percentage styles in bootstrap 3.2

## 0.8.2 (2014-08-02)

* Fix #33 pass `$this` to `update` and `done` callback

## 0.8.1 (2014-08-01)

* Fix #35 wrong amount calculation for `aria-valuemin` != 0

## 0.8.0 (2014-08-01)

* Add bootstrap 3.2.0 styles
* Extend `amount_format` hook for min value
* Update demo page
* Remove unused travis badge
* Fix #27 switch `aria-valuetransitionsgoal` to `data-transitionsgoal`

### 0.7.1 (2014-03-05)

* Add bootstrap 3.1.1 styles
* Switch to Gulp
* Remove bs dotfiles
* Fix #30 compile errors with old markup
* Fix #29 wrong style generation
* Fix #28 element creation which will brick with `django-compressor`

### 0.7.0 (2014-02-11)

* Add bootstrap 3.0.0-3.1.0 styles
* Fix #22 wrong vertical styles for bootstrap 2.x
* Fix #18 missing css requirement documentation

### 0.6.0 (2013-08-16)

* Add bootstrap 3 support
* Add `noConflict` fallback
* Add striped styles
* Add custom string formatting
* Rename `filled` option to `fill`
* Switch `display_text` option dict to string setting
* Switch from `data` to `aria` attributes
* Switch license from Apache2 to MIT
* Fix some minor style issues

### 0.5.0 (2012-08-28)

* Add vertical progressbars
* Add right alignment
* Move styles to css/less
* Fix some code formatting
* Fix different font sizes

### 0.4.6 (2012-06-27)

* Fix flickering on centered text on multiple trigger (due to last fix)

### 0.4.5 (2012-06-25)

* Fix stacking bug on centered text on multiple trigger

### 0.4.4 (2012-06-13)

* Fix caching bug with data method
* Add multitrigger support

### 0.4.3 (2012-06-11)

* Switch from jQuery's `*.attr('data-*')` to `*.data('*')`

### 0.4.2 (2012-06-07)

* Start changelog
* Switch to github markdown
