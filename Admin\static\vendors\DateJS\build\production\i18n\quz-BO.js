/* 
 * DateJS Culture String File
 * Country Code: quz-BO
 * Name: Quechua (Bolivia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["quz-BO"] = {
        "name": "quz-BO",
        "englishName": "Quechua (Bolivia)",
        "nativeName": "runasimi (Bolivia Suyu)",
        "Sunday": "intichaw",
        "Monday": "killachaw",
        "Tuesday": "atipachaw",
        "Wednesday": "quyllurchaw",
        "Thursday": "Ch' askachaw",
        "Friday": "Illapachaw",
        "Saturday": "k'uychichaw",
        "Sun": "int",
        "Mon": "kil",
        "Tue": "ati",
        "Wed": "quy",
        "Thu": "Ch",
        "Fri": "Ill",
        "Sat": "k'u",
        "Su": "int",
        "Mo": "kil",
        "Tu": "ati",
        "We": "quy",
        "Th": "Ch",
        "Fr": "Ill",
        "Sa": "k'u",
        "S_Sun_Initial": "i",
        "M_Mon_Initial": "k",
        "T_Tue_Initial": "a",
        "W_Wed_Initial": "q",
        "T_Thu_Initial": "C",
        "F_Fri_Initial": "I",
        "S_Sat_Initial": "k",
        "January": "Qulla puquy",
        "February": "Hatun puquy",
        "March": "Pauqar waray",
        "April": "ayriwa",
        "May": "Aymuray",
        "June": "Inti raymi",
        "July": "Anta Sitwa",
        "August": "Qhapaq Sitwa",
        "September": "Uma raymi",
        "October": "Kantaray",
        "November": "Ayamarq'a",
        "December": "Kapaq Raymi",
        "Jan_Abbr": "Qul",
        "Feb_Abbr": "Hat",
        "Mar_Abbr": "Pau",
        "Apr_Abbr": "ayr",
        "May_Abbr": "Aym",
        "Jun_Abbr": "Int",
        "Jul_Abbr": "Ant",
        "Aug_Abbr": "Qha",
        "Sep_Abbr": "Uma",
        "Oct_Abbr": "Kan",
        "Nov_Abbr": "Aya",
        "Dec_Abbr": "Kap",
        "AM": "a.m.",
        "PM": "p.m.",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, dd' de 'MMMM' de 'yyyy",
        "h:mm tt": "hh:mm:ss tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, dd' de 'MMMM' de 'yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM' de 'yyyy",
        "/jan(uary)?/": "qul(la puquy)?",
        "/feb(ruary)?/": "hat(un puquy)?",
        "/mar(ch)?/": "pau(qar waray)?",
        "/apr(il)?/": "ayr(iwa)?",
        "/may/": "aym(uray)?",
        "/jun(e)?/": "int(i raymi)?",
        "/jul(y)?/": "ant(a sitwa)?",
        "/aug(ust)?/": "qha(paq sitwa)?",
        "/sep(t(ember)?)?/": "uma( raymi)?",
        "/oct(ober)?/": "kan(taray)?",
        "/nov(ember)?/": "aya(marq'a)?",
        "/dec(ember)?/": "kap(aq raymi)?",
        "/^su(n(day)?)?/": "^intichaw",
        "/^mo(n(day)?)?/": "^killachaw",
        "/^tu(e(s(day)?)?)?/": "^atipachaw",
        "/^we(d(nesday)?)?/": "^quyllurchaw",
        "/^th(u(r(s(day)?)?)?)?/": "^ch' askachaw",
        "/^fr(i(day)?)?/": "^illapachaw",
        "/^sa(t(urday)?)?/": "^k'uychichaw",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "quz-BO";
