/* 
 * DateJS Culture String File
 * Country Code: is-IS
 * Name: Icelandic (Iceland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["is-IS"] = {
        "name": "is-IS",
        "englishName": "Icelandic (Iceland)",
        "nativeName": "íslenska (Ísland)",
        "Sunday": "sunnudagur",
        "Monday": "mánudagur",
        "Tuesday": "þriðjudagur",
        "Wednesday": "miðvikudagur",
        "Thursday": "fimmtudagur",
        "Friday": "föstudagur",
        "Saturday": "laugardagur",
        "Sun": "sun.",
        "Mon": "mán.",
        "Tue": "þri.",
        "Wed": "mið.",
        "Thu": "fim.",
        "Fri": "fös.",
        "Sat": "lau.",
        "Su": "su",
        "Mo": "má",
        "Tu": "þr",
        "We": "mi",
        "Th": "fi",
        "Fr": "fö",
        "Sa": "la",
        "S_Sun_Initial": "s",
        "M_Mon_Initial": "m",
        "T_Tue_Initial": "þ",
        "W_Wed_Initial": "m",
        "T_Thu_Initial": "f",
        "F_Fri_Initial": "f",
        "S_Sat_Initial": "l",
        "January": "janúar",
        "February": "febrúar",
        "March": "mars",
        "April": "apríl",
        "May": "maí",
        "June": "júní",
        "July": "júlí",
        "August": "ágúst",
        "September": "september",
        "October": "október",
        "November": "nóvember",
        "December": "desember",
        "Jan_Abbr": "jan.",
        "Feb_Abbr": "feb.",
        "Mar_Abbr": "mar.",
        "Apr_Abbr": "apr.",
        "May_Abbr": "maí",
        "Jun_Abbr": "jún.",
        "Jul_Abbr": "júl.",
        "Aug_Abbr": "ágú.",
        "Sep_Abbr": "sep.",
        "Oct_Abbr": "okt.",
        "Nov_Abbr": "nóv.",
        "Dec_Abbr": "des.",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jan(.(úar)?)?",
        "/feb(ruary)?/": "feb(.(rúar)?)?",
        "/mar(ch)?/": "mar(.(s)?)?",
        "/apr(il)?/": "apr(.(íl)?)?",
        "/may/": "maí",
        "/jun(e)?/": "jún(.(í)?)?",
        "/jul(y)?/": "júl(.(í)?)?",
        "/aug(ust)?/": "ágú(.(st)?)?",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "okt(.(óber)?)?",
        "/nov(ember)?/": "nóv(.(ember)?)?",
        "/dec(ember)?/": "des(.(ember)?)?",
        "/^su(n(day)?)?/": "^su(n(.(nudagur)?)?)?",
        "/^mo(n(day)?)?/": "^má(n(.(udagur)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^þr(i(.(ðjudagur)?)?)?",
        "/^we(d(nesday)?)?/": "^mi(ð(.(vikudagur)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^fi(m(.(mtudagur)?)?)?",
        "/^fr(i(day)?)?/": "^fö(s(.(tudagur)?)?)?",
        "/^sa(t(urday)?)?/": "^la(u(.(gardagur)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "is-IS";
