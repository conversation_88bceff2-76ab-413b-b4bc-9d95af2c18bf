/* 
 * DateJS Culture String File
 * Country Code: he-IL
 * Name: Hebrew (Israel)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["he-IL"] = {
        "name": "he-IL",
        "englishName": "Hebrew (Israel)",
        "nativeName": "עברית (ישראל)",
        "Sunday": "יום ראשון",
        "Monday": "יום שני",
        "Tuesday": "יום שלישי",
        "Wednesday": "יום רביעי",
        "Thursday": "יום חמישי",
        "Friday": "יום שישי",
        "Saturday": "שבת",
        "Sun": "יום א",
        "Mon": "יום ב",
        "Tue": "יום ג",
        "Wed": "יום ד",
        "Thu": "יום ה",
        "Fri": "יום ו",
        "Sat": "שבת",
        "Su": "א",
        "Mo": "ב",
        "Tu": "ג",
        "We": "ד",
        "Th": "ה",
        "Fr": "ו",
        "Sa": "ש",
        "S_Sun_Initial": "א",
        "M_Mon_Initial": "ב",
        "T_Tue_Initial": "ג",
        "W_Wed_Initial": "ד",
        "T_Thu_Initial": "ה",
        "F_Fri_Initial": "ו",
        "S_Sat_Initial": "ש",
        "January": "ינואר",
        "February": "פברואר",
        "March": "מרץ",
        "April": "אפריל",
        "May": "מאי",
        "June": "יוני",
        "July": "יולי",
        "August": "אוגוסט",
        "September": "ספטמבר",
        "October": "אוקטובר",
        "November": "נובמבר",
        "December": "דצמבר",
        "Jan_Abbr": "ינו",
        "Feb_Abbr": "פבר",
        "Mar_Abbr": "מרץ",
        "Apr_Abbr": "אפר",
        "May_Abbr": "מאי",
        "Jun_Abbr": "יונ",
        "Jul_Abbr": "יול",
        "Aug_Abbr": "אוג",
        "Sep_Abbr": "ספט",
        "Oct_Abbr": "אוק",
        "Nov_Abbr": "נוב",
        "Dec_Abbr": "דצמ",
        "AM": "AM",
        "PM": "PM",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dddd dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ינו(אר)?",
        "/feb(ruary)?/": "פבר(ואר)?",
        "/mar(ch)?/": "מרץ",
        "/apr(il)?/": "אפר(יל)?",
        "/may/": "מאי",
        "/jun(e)?/": "יונ(י)?",
        "/jul(y)?/": "יול(י)?",
        "/aug(ust)?/": "אוג(וסט)?",
        "/sep(t(ember)?)?/": "ספט(מבר)?",
        "/oct(ober)?/": "אוק(טובר)?",
        "/nov(ember)?/": "נוב(מבר)?",
        "/dec(ember)?/": "דצמ(בר)?",
        "/^su(n(day)?)?/": "^א(ום א(אשון)?)?",
        "/^mo(n(day)?)?/": "^ב(ום ב(ני)?)?",
        "/^tu(e(s(day)?)?)?/": "^ג(ום ג(לישי)?)?",
        "/^we(d(nesday)?)?/": "^ד(ום ד(ביעי)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ה(ום ה(מישי)?)?",
        "/^fr(i(day)?)?/": "^ו(ום ו(ישי)?)?",
        "/^sa(t(urday)?)?/": "^ש(1)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "he-IL";
