/* 
 * DateJS Culture String File
 * Country Code: pt-PT
 * Name: Portuguese (Portugal)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["pt-PT"] = {
        "name": "pt-PT",
        "englishName": "Portuguese (Portugal)",
        "nativeName": "Português (Portugal)",
        "Sunday": "domingo",
        "Monday": "segunda-feira",
        "Tuesday": "terça-feira",
        "Wednesday": "quarta-feira",
        "Thursday": "quinta-feira",
        "Friday": "sexta-feira",
        "Saturday": "sábado",
        "Sun": "dom",
        "Mon": "seg",
        "Tue": "ter",
        "Wed": "qua",
        "Thu": "qui",
        "Fri": "sex",
        "Sat": "sáb",
        "Su": "dom",
        "Mo": "seg",
        "Tu": "ter",
        "We": "qua",
        "Th": "qui",
        "Fr": "sex",
        "Sa": "sáb",
        "S_Sun_Initial": "d",
        "M_Mon_Initial": "s",
        "T_Tue_Initial": "t",
        "W_Wed_Initial": "q",
        "T_Thu_Initial": "q",
        "F_Fri_Initial": "s",
        "S_Sat_Initial": "s",
        "January": "Janeiro",
        "February": "Fevereiro",
        "March": "Março",
        "April": "Abril",
        "May": "Maio",
        "June": "Junho",
        "July": "Julho",
        "August": "Agosto",
        "September": "Setembro",
        "October": "Outubro",
        "November": "Novembro",
        "December": "Dezembro",
        "Jan_Abbr": "Jan",
        "Feb_Abbr": "Fev",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Abr",
        "May_Abbr": "Mai",
        "Jun_Abbr": "Jun",
        "Jul_Abbr": "Jul",
        "Aug_Abbr": "Ago",
        "Sep_Abbr": "Set",
        "Oct_Abbr": "Out",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Dez",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d' de 'MMMM' de 'yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d' de 'MMMM' de 'yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d/M",
        "MMMM, yyyy": "MMMM' de 'yyyy",
        "/jan(uary)?/": "jan(eiro)?",
        "/feb(ruary)?/": "fev(ereiro)?",
        "/mar(ch)?/": "mar(ço)?",
        "/apr(il)?/": "abr(il)?",
        "/may/": "mai(o)?",
        "/jun(e)?/": "jun(ho)?",
        "/jul(y)?/": "jul(ho)?",
        "/aug(ust)?/": "ago(sto)?",
        "/sep(t(ember)?)?/": "set(embro)?",
        "/oct(ober)?/": "out(ubro)?",
        "/nov(ember)?/": "nov(embro)?",
        "/dec(ember)?/": "dez(embro)?",
        "/^su(n(day)?)?/": "^domingo",
        "/^mo(n(day)?)?/": "^segunda-feira",
        "/^tu(e(s(day)?)?)?/": "^terça-feira",
        "/^we(d(nesday)?)?/": "^quarta-feira",
        "/^th(u(r(s(day)?)?)?)?/": "^quinta-feira",
        "/^fr(i(day)?)?/": "^sexta-feira",
        "/^sa(t(urday)?)?/": "^sábado",
        "/^next/": "^prox(im(o(s)?|a(s)?))?",
        "/^last|past|prev(ious)?/": "^ant(erior(es)?)?|ult(im(o(s)?|a(s)?))?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|depois)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|antes)",
        "/^yes(terday)?/": "^ontem",
        "/^t(od(ay)?)?/": "^h(oje)?",
        "/^tom(orrow)?/": "^amanha",
        "/^n(ow)?/": "^a(gora)?",
        "/^ms|milli(second)?s?/": "^ms|milli(segundo)?s?",
        "/^sec(ond)?s?/": "^s(egundo)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(uto)?s?",
        "/^h(our)?s?/": "^h(ora)?s?",
        "/^w(eek)?s?/": "^sem(ana)?s?",
        "/^m(onth)?s?/": "^m(e(se)?s?)?",
        "/^d(ay)?s?/": "^d(ia(s)?s?)?",
        "/^y(ear)?s?/": "^an((o)?s?)?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "pt-PT";
