/* 
 * DateJS Culture String File
 * Country Code: az-Cyrl-AZ
 * Name: Azeri (Cyrillic, Azerbaijan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["az-Cyrl-AZ"] = {
        "name": "az-Cyrl-AZ",
        "englishName": "Azeri (Cyrillic, Azerbaijan)",
        "nativeName": "Азәрбајҹан (Азәрбајҹан)",
        "Sunday": "Базар",
        "Monday": "Базар ертәси",
        "Tuesday": "Чәршәнбә ахшамы",
        "Wednesday": "Чәршәнбә",
        "Thursday": "Ҹүмә ахшамы",
        "Friday": "Ҹүмә",
        "Saturday": "Шәнбә",
        "Sun": "Б",
        "Mon": "Бе",
        "Tue": "Ча",
        "Wed": "Ч",
        "Thu": "Ҹа",
        "Fri": "Ҹ",
        "Sat": "Ш",
        "Su": "Б",
        "Mo": "Бе",
        "Tu": "Ча",
        "We": "Ч",
        "Th": "Ҹа",
        "Fr": "Ҹ",
        "Sa": "Ш",
        "S_Sun_Initial": "Б",
        "M_Mon_Initial": "Б",
        "T_Tue_Initial": "Ч",
        "W_Wed_Initial": "Ч",
        "T_Thu_Initial": "Ҹ",
        "F_Fri_Initial": "Ҹ",
        "S_Sat_Initial": "Ш",
        "January": "Јанвар",
        "February": "Феврал",
        "March": "Март",
        "April": "Апрел",
        "May": "Мај",
        "June": "Ијун",
        "July": "Ијул",
        "August": "Август",
        "September": "Сентјабр",
        "October": "Октјабр",
        "November": "Нојабр",
        "December": "Декабр",
        "Jan_Abbr": "Јан",
        "Feb_Abbr": "Фев",
        "Mar_Abbr": "Мар",
        "Apr_Abbr": "Апр",
        "May_Abbr": "Мај",
        "Jun_Abbr": "Ијун",
        "Jul_Abbr": "Ијул",
        "Aug_Abbr": "Авг",
        "Sep_Abbr": "Сен",
        "Oct_Abbr": "Окт",
        "Nov_Abbr": "Ноя",
        "Dec_Abbr": "Дек",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "јан(вар)?",
        "/feb(ruary)?/": "фев(рал)?",
        "/mar(ch)?/": "мар(т)?",
        "/apr(il)?/": "апр(ел)?",
        "/may/": "мај",
        "/jun(e)?/": "ијун",
        "/jul(y)?/": "ијул",
        "/aug(ust)?/": "авг(уст)?",
        "/sep(t(ember)?)?/": "сен(тјабр)?",
        "/oct(ober)?/": "окт(јабр)?",
        "/nov(ember)?/": "нојабр",
        "/dec(ember)?/": "дек(абр)?",
        "/^su(n(day)?)?/": "^базар",
        "/^mo(n(day)?)?/": "^базар ертәси",
        "/^tu(e(s(day)?)?)?/": "^чәршәнбә ахшамы",
        "/^we(d(nesday)?)?/": "^чәршәнбә",
        "/^th(u(r(s(day)?)?)?)?/": "^ҹүмә ахшамы",
        "/^fr(i(day)?)?/": "^ҹүмә",
        "/^sa(t(urday)?)?/": "^шәнбә",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "az-Cyrl-AZ";
