/* 
 * DateJS Culture String File
 * Country Code: ur-PK
 * Name: Urdu (Islamic Republic of Pakistan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ur-PK"] = {
        "name": "ur-P<PERSON>",
        "englishName": "Urdu (Islamic Republic of Pakistan)",
        "nativeName": "اُردو (پاکستان)",
        "Sunday": "اتوار",
        "Monday": "پير",
        "Tuesday": "منگل",
        "Wednesday": "بدھ",
        "Thursday": "جمعرات",
        "Friday": "جمعه",
        "Saturday": "هفته",
        "Sun": "اتوار",
        "Mon": "پير",
        "Tue": "منگل",
        "Wed": "بدھ",
        "Thu": "جمعرات",
        "Fri": "جمعه",
        "Sat": "هفته",
        "Su": "ا",
        "Mo": "پ",
        "Tu": "م",
        "We": "ب",
        "Th": "ج",
        "Fr": "ج",
        "Sa": "ه",
        "S_Sun_Initial": "ا",
        "M_Mon_Initial": "پ",
        "T_Tue_Initial": "م",
        "W_Wed_Initial": "ب",
        "T_Thu_Initial": "ج",
        "F_Fri_Initial": "ج",
        "S_Sat_Initial": "ه",
        "January": "جنورى",
        "February": "فرورى",
        "March": "مارچ",
        "April": "اپريل",
        "May": "مئ",
        "June": "جون",
        "July": "جولاٸ",
        "August": "اگست",
        "September": "ستمبر",
        "October": "اکتوبر",
        "November": "نومبر",
        "December": "دسمبر",
        "Jan_Abbr": "جنورى",
        "Feb_Abbr": "فرورى",
        "Mar_Abbr": "مارچ",
        "Apr_Abbr": "اپريل",
        "May_Abbr": "مئ",
        "Jun_Abbr": "جون",
        "Jul_Abbr": "جولاٸ",
        "Aug_Abbr": "اگست",
        "Sep_Abbr": "ستمبر",
        "Oct_Abbr": "اکتوبر",
        "Nov_Abbr": "نومبر",
        "Dec_Abbr": "دسمبر",
        "AM": "AM",
        "PM": "PM",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM, yyyy",
        "h:mm tt": "h:mm tt",
        "h:mm:ss tt": "h:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM, yyyy h:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "جنورى",
        "/feb(ruary)?/": "فرورى",
        "/mar(ch)?/": "مارچ",
        "/apr(il)?/": "اپريل",
        "/may/": "مئ",
        "/jun(e)?/": "جون",
        "/jul(y)?/": "جولاٸ",
        "/aug(ust)?/": "اگست",
        "/sep(t(ember)?)?/": "ستمبر",
        "/oct(ober)?/": "اکتوبر",
        "/nov(ember)?/": "نومبر",
        "/dec(ember)?/": "دسمبر",
        "/^su(n(day)?)?/": "^ا(1)?",
        "/^mo(n(day)?)?/": "^پ(1)?",
        "/^tu(e(s(day)?)?)?/": "^م(1)?",
        "/^we(d(nesday)?)?/": "^ب(1)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ج(1)?",
        "/^fr(i(day)?)?/": "^ج(1)?",
        "/^sa(t(urday)?)?/": "^ه(1)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ur-PK";
