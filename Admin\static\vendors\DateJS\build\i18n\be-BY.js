/* 
 * DateJS Culture String File
 * Country Code: be-BY
 * Name: Belarusian (Belarus)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["be-BY"] = {
        "name": "be-BY",
        "englishName": "Belarusian (Belarus)",
        "nativeName": "Беларускі (Беларусь)",
        "Sunday": "нядзеля",
        "Monday": "панядзелак",
        "Tuesday": "аўторак",
        "Wednesday": "серада",
        "Thursday": "чацвер",
        "Friday": "пятніца",
        "Saturday": "субота",
        "Sun": "нд",
        "Mon": "пн",
        "Tue": "аў",
        "Wed": "ср",
        "Thu": "чц",
        "Fri": "пт",
        "Sat": "сб",
        "Su": "нд",
        "Mo": "пн",
        "Tu": "аў",
        "We": "ср",
        "Th": "чц",
        "Fr": "пт",
        "Sa": "сб",
        "S_Sun_Initial": "н",
        "M_Mon_Initial": "п",
        "T_Tue_Initial": "а",
        "W_Wed_Initial": "с",
        "T_Thu_Initial": "ч",
        "F_Fri_Initial": "п",
        "S_Sat_Initial": "с",
        "January": "Студзень",
        "February": "Люты",
        "March": "Сакавік",
        "April": "Красавік",
        "May": "Май",
        "June": "Чэрвень",
        "July": "Ліпень",
        "August": "Жнівень",
        "September": "Верасень",
        "October": "Кастрычнік",
        "November": "Лістапад",
        "December": "Снежань",
        "Jan_Abbr": "Сту",
        "Feb_Abbr": "Лют",
        "Mar_Abbr": "Сак",
        "Apr_Abbr": "Кра",
        "May_Abbr": "Май",
        "Jun_Abbr": "Чэр",
        "Jul_Abbr": "Ліп",
        "Aug_Abbr": "Жні",
        "Sep_Abbr": "Вер",
        "Oct_Abbr": "Кас",
        "Nov_Abbr": "Ліс",
        "Dec_Abbr": "Сне",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "сту(дзень)?",
        "/feb(ruary)?/": "лют(ы)?",
        "/mar(ch)?/": "сак(авік)?",
        "/apr(il)?/": "кра(савік)?",
        "/may/": "май",
        "/jun(e)?/": "чэр(вень)?",
        "/jul(y)?/": "ліп(ень)?",
        "/aug(ust)?/": "жні(вень)?",
        "/sep(t(ember)?)?/": "вер(асень)?",
        "/oct(ober)?/": "кас(трычнік)?",
        "/nov(ember)?/": "ліс(тапад)?",
        "/dec(ember)?/": "сне(жань)?",
        "/^su(n(day)?)?/": "^нядзеля",
        "/^mo(n(day)?)?/": "^панядзелак",
        "/^tu(e(s(day)?)?)?/": "^аўторак",
        "/^we(d(nesday)?)?/": "^серада",
        "/^th(u(r(s(day)?)?)?)?/": "^чацвер",
        "/^fr(i(day)?)?/": "^пятніца",
        "/^sa(t(urday)?)?/": "^субота",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "be-BY";
