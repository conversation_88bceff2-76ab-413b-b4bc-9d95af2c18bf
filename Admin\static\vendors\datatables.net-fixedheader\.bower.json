{"name": "datatables.net-fixedheader", "description": "FixedHeader for DataTables ", "main": ["js/dataTables.fixedHeader.js"], "keywords": ["fixed headers", "sticky", "DataTables", "j<PERSON><PERSON><PERSON>", "table", "DataTables"], "dependencies": {"jquery": ">=1.7", "datatables.net": ">=1.10.9"}, "moduleType": ["globals", "amd", "node"], "ignore": ["composer.json", "datatables.json", "package.json"], "authors": [{"name": "SpryMedia Ltd", "homepage": "https://datatables.net"}], "homepage": "https://datatables.net", "license": "MIT", "version": "3.1.2", "_release": "3.1.2", "_resolution": {"type": "version", "tag": "3.1.2", "commit": "762ce1822c9b10180c4359ab4036a0c78d846e73"}, "_source": "https://github.com/DataTables/Dist-DataTables-FixedHeader.git", "_target": "^3.1.1", "_originalSource": "datatables.net-fixedheader"}