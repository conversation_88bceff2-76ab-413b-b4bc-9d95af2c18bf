/* 
 * DateJS Culture String File
 * Country Code: et-EE
 * Name: Estonian (Estonia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["et-EE"] = {
        "name": "et-EE",
        "englishName": "Estonian (Estonia)",
        "nativeName": "eesti (Eesti)",
        "Sunday": "pühapäev",
        "Monday": "esmaspäev",
        "Tuesday": "teisipäev",
        "Wednesday": "kolmapäev",
        "Thursday": "neljapäev",
        "Friday": "reede",
        "Saturday": "laupäev",
        "Sun": "P",
        "Mon": "E",
        "Tue": "T",
        "Wed": "K",
        "Thu": "N",
        "Fri": "R",
        "Sat": "L",
        "Su": "P",
        "Mo": "E",
        "Tu": "T",
        "We": "K",
        "Th": "N",
        "Fr": "R",
        "Sa": "L",
        "S_Sun_Initial": "P",
        "M_Mon_Initial": "E",
        "T_Tue_Initial": "T",
        "W_Wed_Initial": "K",
        "T_Thu_Initial": "N",
        "F_Fri_Initial": "R",
        "S_Sat_Initial": "L",
        "January": "jaanuar",
        "February": "veebruar",
        "March": "märts",
        "April": "aprill",
        "May": "mai",
        "June": "juuni",
        "July": "juuli",
        "August": "august",
        "September": "september",
        "October": "oktoober",
        "November": "november",
        "December": "detsember",
        "Jan_Abbr": "jaan",
        "Feb_Abbr": "veebr",
        "Mar_Abbr": "märts",
        "Apr_Abbr": "apr",
        "May_Abbr": "mai",
        "Jun_Abbr": "juuni",
        "Jul_Abbr": "juuli",
        "Aug_Abbr": "aug",
        "Sep_Abbr": "sept",
        "Oct_Abbr": "okt",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dets",
        "AM": "EL",
        "PM": "PL",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy'. a.'",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy'. a.' H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM",
        "MMMM, yyyy": "MMMM yyyy'. a.'",
        "/jan(uary)?/": "jaan(uar)?",
        "/feb(ruary)?/": "veebr(uar)?",
        "/mar(ch)?/": "märts",
        "/apr(il)?/": "apr(ill)?",
        "/may/": "mai",
        "/jun(e)?/": "juuni",
        "/jul(y)?/": "juuli",
        "/aug(ust)?/": "aug(ust)?",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "okt(oober)?",
        "/nov(ember)?/": "nov(ember)?",
        "/dec(ember)?/": "dets(ember)?",
        "/^su(n(day)?)?/": "^pühapäev",
        "/^mo(n(day)?)?/": "^esmaspäev",
        "/^tu(e(s(day)?)?)?/": "^teisipäev",
        "/^we(d(nesday)?)?/": "^kolmapäev",
        "/^th(u(r(s(day)?)?)?)?/": "^neljapäev",
        "/^fr(i(day)?)?/": "^reede",
        "/^sa(t(urday)?)?/": "^laupäev",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "et-EE";
