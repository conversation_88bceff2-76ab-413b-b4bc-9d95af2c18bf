/* 
 * DateJS Culture String File
 * Country Code: fi-FI
 * Name: Finnish (Finland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["fi-FI"] = {
        "name": "fi-FI",
        "englishName": "Finnish (Finland)",
        "nativeName": "suomi (Suomi)",
        "Sunday": "sunnuntai",
        "Monday": "maanantai",
        "Tuesday": "tiistai",
        "Wednesday": "keskiviikko",
        "Thursday": "torstai",
        "Friday": "perjantai",
        "Saturday": "lauantai",
        "Sun": "su",
        "Mon": "ma",
        "Tue": "ti",
        "Wed": "ke",
        "Thu": "to",
        "Fri": "pe",
        "Sat": "la",
        "Su": "su",
        "Mo": "ma",
        "Tu": "ti",
        "We": "ke",
        "Th": "to",
        "Fr": "pe",
        "Sa": "la",
        "S_Sun_Initial": "s",
        "M_Mon_Initial": "m",
        "T_Tue_Initial": "t",
        "W_Wed_Initial": "k",
        "T_Thu_Initial": "t",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "l",
        "January": "tammikuu",
        "February": "helmikuu",
        "March": "maaliskuu",
        "April": "huhtikuu",
        "May": "toukokuu",
        "June": "kesäkuu",
        "July": "heinäkuu",
        "August": "elokuu",
        "September": "syyskuu",
        "October": "lokakuu",
        "November": "marraskuu",
        "December": "joulukuu",
        "Jan_Abbr": "tammi",
        "Feb_Abbr": "helmi",
        "Mar_Abbr": "maalis",
        "Apr_Abbr": "huhti",
        "May_Abbr": "touko",
        "Jun_Abbr": "kesä",
        "Jul_Abbr": "heinä",
        "Aug_Abbr": "elo",
        "Sep_Abbr": "syys",
        "Oct_Abbr": "loka",
        "Nov_Abbr": "marras",
        "Dec_Abbr": "joulu",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM'ta 'yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM'ta 'yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d. MMMM'ta'",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "tammi(kuu)?",
        "/feb(ruary)?/": "helmi(kuu)?",
        "/mar(ch)?/": "maalis(kuu)?",
        "/apr(il)?/": "huhti(kuu)?",
        "/may/": "touko(kuu)?",
        "/jun(e)?/": "kesä(kuu)?",
        "/jul(y)?/": "heinä(kuu)?",
        "/aug(ust)?/": "elo(kuu)?",
        "/sep(t(ember)?)?/": "syys(kuu)?",
        "/oct(ober)?/": "loka(kuu)?",
        "/nov(ember)?/": "marras(kuu)?",
        "/dec(ember)?/": "joulu(kuu)?",
        "/^su(n(day)?)?/": "^sunnuntai",
        "/^mo(n(day)?)?/": "^maanantai",
        "/^tu(e(s(day)?)?)?/": "^tiistai",
        "/^we(d(nesday)?)?/": "^keskiviikko",
        "/^th(u(r(s(day)?)?)?)?/": "^torstai",
        "/^fr(i(day)?)?/": "^perjantai",
        "/^sa(t(urday)?)?/": "^lauantai",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "fi-FI";
