/* 
 * DateJS Culture String File
 * Country Code: az-Latn-AZ
 * Name: Azeri (Latin, Azerbaijan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["az-Latn-AZ"] = {
        "name": "az-Latn-AZ",
        "englishName": "Azeri (Latin, Azerbaijan)",
        "nativeName": "Azərbaycan­ılı (Azərbaycanca)",
        "Sunday": "Bazar",
        "Monday": "<PERSON>zar ertəsi",
        "Tuesday": "Çərşənbə axşamı",
        "Wednesday": "Çərşənbə",
        "Thursday": "Cümə axşamı",
        "Friday": "Cümə",
        "Saturday": "Şənbə",
        "Sun": "B",
        "Mon": "Be",
        "Tue": "Ça",
        "Wed": "Ç",
        "Thu": "Ca",
        "Fri": "C",
        "Sat": "Ş",
        "Su": "B",
        "Mo": "Be",
        "Tu": "Ça",
        "We": "Ç",
        "Th": "Ca",
        "Fr": "C",
        "Sa": "Ş",
        "S_Sun_Initial": "B",
        "M_Mon_Initial": "B",
        "T_Tue_Initial": "Ç",
        "W_Wed_Initial": "Ç",
        "T_Thu_Initial": "C",
        "F_Fri_Initial": "C",
        "S_Sat_Initial": "Ş",
        "January": "Yanvar",
        "February": "Fevral",
        "March": "Mart",
        "April": "Aprel",
        "May": "May",
        "June": "İyun",
        "July": "İyul",
        "August": "Avgust",
        "September": "Sentyabr",
        "October": "Oktyabr",
        "November": "Noyabr",
        "December": "Dekabr",
        "Jan_Abbr": "Yan",
        "Feb_Abbr": "Fev",
        "Mar_Abbr": "Mar",
        "Apr_Abbr": "Apr",
        "May_Abbr": "May",
        "Jun_Abbr": "İyun",
        "Jul_Abbr": "İyul",
        "Aug_Abbr": "Avg",
        "Sep_Abbr": "Sen",
        "Oct_Abbr": "Okt",
        "Nov_Abbr": "Noy",
        "Dec_Abbr": "Dek",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "yan(var)?",
        "/feb(ruary)?/": "fev(ral)?",
        "/mar(ch)?/": "mar(t)?",
        "/apr(il)?/": "apr(el)?",
        "/may/": "may",
        "/jun(e)?/": "iyun",
        "/jul(y)?/": "iyul",
        "/aug(ust)?/": "avg(ust)?",
        "/sep(t(ember)?)?/": "sen(tyabr)?",
        "/oct(ober)?/": "okt(yabr)?",
        "/nov(ember)?/": "noy(abr)?",
        "/dec(ember)?/": "dek(abr)?",
        "/^su(n(day)?)?/": "^bazar",
        "/^mo(n(day)?)?/": "^bazar ertəsi",
        "/^tu(e(s(day)?)?)?/": "^çərşənbə axşamı",
        "/^we(d(nesday)?)?/": "^çərşənbə",
        "/^th(u(r(s(day)?)?)?)?/": "^cümə axşamı",
        "/^fr(i(day)?)?/": "^cümə",
        "/^sa(t(urday)?)?/": "^şənbə",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "az-Latn-AZ";
