/* 
 * DateJS Culture String File
 * Country Code: es-AR
 * Name: Spanish (Argentina)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["es-AR"] = {
        "name": "es-AR",
        "englishName": "Spanish (Argentina)",
        "nativeName": "Español (Argentina)",
        "Sunday": "domingo",
        "Monday": "lunes",
        "Tuesday": "martes",
        "Wednesday": "miércoles",
        "Thursday": "jueves",
        "Friday": "viernes",
        "Saturday": "sábado",
        "Sun": "dom",
        "Mon": "lun",
        "Tue": "mar",
        "Wed": "mié",
        "Thu": "jue",
        "Fri": "vie",
        "Sat": "sáb",
        "Su": "do",
        "Mo": "lu",
        "Tu": "ma",
        "We": "mi",
        "Th": "ju",
        "Fr": "vi",
        "Sa": "sá",
        "S_Sun_Initial": "d",
        "M_Mon_Initial": "l",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "m",
        "T_Thu_Initial": "j",
        "F_Fri_Initial": "v",
        "S_Sat_Initial": "s",
        "January": "enero",
        "February": "febrero",
        "March": "marzo",
        "April": "abril",
        "May": "mayo",
        "June": "junio",
        "July": "julio",
        "August": "agosto",
        "September": "septiembre",
        "October": "octubre",
        "November": "noviembre",
        "December": "diciembre",
        "Jan_Abbr": "ene",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "abr",
        "May_Abbr": "may",
        "Jun_Abbr": "jun",
        "Jul_Abbr": "jul",
        "Aug_Abbr": "ago",
        "Sep_Abbr": "sep",
        "Oct_Abbr": "oct",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dic",
        "AM": "a.m.",
        "PM": "p.m.",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, dd' de 'MMMM' de 'yyyy",
        "h:mm tt": "hh:mm tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, dd' de 'MMMM' de 'yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM' de 'yyyy",
        "/jan(uary)?/": "ene(ro)?",
        "/feb(ruary)?/": "feb(rero)?",
        "/mar(ch)?/": "mar(zo)?",
        "/apr(il)?/": "abr(il)?",
        "/may/": "may(o)?",
        "/jun(e)?/": "jun(io)?",
        "/jul(y)?/": "jul(io)?",
        "/aug(ust)?/": "ago(sto)?",
        "/sep(t(ember)?)?/": "sep(tiembre)?",
        "/oct(ober)?/": "oct(ubre)?",
        "/nov(ember)?/": "nov(iembre)?",
        "/dec(ember)?/": "dic(iembre)?",
        "/^su(n(day)?)?/": "^do(m(ingo)?)?",
        "/^mo(n(day)?)?/": "^lu(n(es)?)?",
        "/^tu(e(s(day)?)?)?/": "^ma(r(tes)?)?",
        "/^we(d(nesday)?)?/": "^mi(é(rcoles)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ju(e(ves)?)?",
        "/^fr(i(day)?)?/": "^vi(e(rnes)?)?",
        "/^sa(t(urday)?)?/": "^sá(b(ado)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "es-AR";
