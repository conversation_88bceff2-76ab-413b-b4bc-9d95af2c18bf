/* 
 * DateJS Culture String File
 * Country Code: uz-Latn-UZ
 * Name: Uzbek (Latin, Uzbekistan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["uz-Latn-UZ"] = {
        "name": "uz-Latn-UZ",
        "englishName": "Uzbek (Latin, Uzbekistan)",
        "nativeName": "U'zbek (U'zbekiston Respublikasi)",
        "Sunday": "yakshanba",
        "Monday": "dushanba",
        "Tuesday": "seshanba",
        "Wednesday": "chorshanba",
        "Thursday": "payshanba",
        "Friday": "juma",
        "Saturday": "shanba",
        "Sun": "yak.",
        "Mon": "dsh.",
        "Tue": "sesh.",
        "Wed": "chr.",
        "Thu": "psh.",
        "Fri": "jm.",
        "Sat": "sh.",
        "Su": "yak",
        "Mo": "dsh",
        "Tu": "sesh",
        "We": "chr",
        "Th": "psh",
        "Fr": "jm",
        "Sa": "sh",
        "S_Sun_Initial": "y",
        "M_Mon_Initial": "d",
        "T_Tue_Initial": "s",
        "W_Wed_Initial": "c",
        "T_Thu_Initial": "p",
        "F_Fri_Initial": "j",
        "S_Sat_Initial": "s",
        "January": "yanvar",
        "February": "fevral",
        "March": "mart",
        "April": "aprel",
        "May": "may",
        "June": "iyun",
        "July": "iyul",
        "August": "avgust",
        "September": "sentyabr",
        "October": "oktyabr",
        "November": "noyabr",
        "December": "dekabr",
        "Jan_Abbr": "yanvar",
        "Feb_Abbr": "fevral",
        "Mar_Abbr": "mart",
        "Apr_Abbr": "aprel",
        "May_Abbr": "may",
        "Jun_Abbr": "iyun",
        "Jul_Abbr": "iyul",
        "Aug_Abbr": "avgust",
        "Sep_Abbr": "sentyabr",
        "Oct_Abbr": "oktyabr",
        "Nov_Abbr": "noyabr",
        "Dec_Abbr": "dekabr",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM yyyy",
        "dddd, MMMM dd, yyyy": "yyyy 'yil' d-MMMM",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy 'yil' d-MMMM HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d-MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "yanvar",
        "/feb(ruary)?/": "fevral",
        "/mar(ch)?/": "mart",
        "/apr(il)?/": "aprel",
        "/may/": "may",
        "/jun(e)?/": "iyun",
        "/jul(y)?/": "iyul",
        "/aug(ust)?/": "avgust",
        "/sep(t(ember)?)?/": "sentyabr",
        "/oct(ober)?/": "oktyabr",
        "/nov(ember)?/": "noyabr",
        "/dec(ember)?/": "dekabr",
        "/^su(n(day)?)?/": "^yak((.(shanba)?)?)?",
        "/^mo(n(day)?)?/": "^dsh((.(hanba)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^sesh((.(anba)?)?)?",
        "/^we(d(nesday)?)?/": "^chr((.(rshanba)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^psh((.(shanba)?)?)?",
        "/^fr(i(day)?)?/": "^jm((.(ma)?)?)?",
        "/^sa(t(urday)?)?/": "^sh((.(anba)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "uz-Latn-UZ";
