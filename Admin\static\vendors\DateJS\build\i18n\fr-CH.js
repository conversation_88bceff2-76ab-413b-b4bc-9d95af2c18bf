/* 
 * DateJS Culture String File
 * Country Code: fr-CH
 * Name: French (Switzerland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["fr-CH"] = {
        "name": "fr-CH",
        "englishName": "French (Switzerland)",
        "nativeName": "français (Suisse)",
        "Sunday": "dimanche",
        "Monday": "lundi",
        "Tuesday": "mardi",
        "Wednesday": "mercredi",
        "Thursday": "jeudi",
        "Friday": "vendredi",
        "Saturday": "samedi",
        "Sun": "dim.",
        "Mon": "lun.",
        "Tue": "mar.",
        "Wed": "mer.",
        "Thu": "jeu.",
        "Fri": "ven.",
        "Sat": "sam.",
        "Su": "di",
        "Mo": "lu",
        "Tu": "ma",
        "We": "me",
        "Th": "je",
        "Fr": "ve",
        "Sa": "sa",
        "S_Sun_Initial": "d",
        "M_Mon_Initial": "l",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "m",
        "T_Thu_Initial": "j",
        "F_Fri_Initial": "v",
        "S_Sat_Initial": "s",
        "January": "janvier",
        "February": "février",
        "March": "mars",
        "April": "avril",
        "May": "mai",
        "June": "juin",
        "July": "juillet",
        "August": "août",
        "September": "septembre",
        "October": "octobre",
        "November": "novembre",
        "December": "décembre",
        "Jan_Abbr": "janv.",
        "Feb_Abbr": "févr.",
        "Mar_Abbr": "mars",
        "Apr_Abbr": "avr.",
        "May_Abbr": "mai",
        "Jun_Abbr": "juin",
        "Jul_Abbr": "juil.",
        "Aug_Abbr": "août",
        "Sep_Abbr": "sept.",
        "Oct_Abbr": "oct.",
        "Nov_Abbr": "nov.",
        "Dec_Abbr": "déc.",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d. MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d. MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "janv(.(ier)?)?",
        "/feb(ruary)?/": "févr(.(ier)?)?",
        "/mar(ch)?/": "mars",
        "/apr(il)?/": "avr(.(il)?)?",
        "/may/": "mai",
        "/jun(e)?/": "juin",
        "/jul(y)?/": "juil(.(let)?)?",
        "/aug(ust)?/": "août",
        "/sep(t(ember)?)?/": "sept(.(embre)?)?",
        "/oct(ober)?/": "oct(.(obre)?)?",
        "/nov(ember)?/": "nov(.(embre)?)?",
        "/dec(ember)?/": "déc(.(embre)?)?",
        "/^su(n(day)?)?/": "^di(m(.(anche)?)?)?",
        "/^mo(n(day)?)?/": "^lu(n(.(di)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^ma(r(.(di)?)?)?",
        "/^we(d(nesday)?)?/": "^me(r(.(credi)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^je(u(.(di)?)?)?",
        "/^fr(i(day)?)?/": "^ve(n(.(dredi)?)?)?",
        "/^sa(t(urday)?)?/": "^sa(m(.(edi)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "fr-CH";
