/* 
 * DateJS Culture String File
 * Country Code: sk-SK
 * Name: Slovak (Slovakia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sk-SK"] = {
        "name": "sk-SK",
        "englishName": "Slovak (Slovakia)",
        "nativeName": "slovenčina (Slovenská republika)",
        "Sunday": "nedeľa",
        "Monday": "pondelok",
        "Tuesday": "utorok",
        "Wednesday": "streda",
        "Thursday": "štvrtok",
        "Friday": "piatok",
        "Saturday": "sobota",
        "Sun": "ne",
        "Mon": "po",
        "Tue": "ut",
        "Wed": "st",
        "Thu": "št",
        "Fri": "pi",
        "Sat": "so",
        "Su": "ne",
        "Mo": "po",
        "Tu": "ut",
        "We": "st",
        "Th": "št",
        "Fr": "pi",
        "Sa": "so",
        "S_Sun_Initial": "n",
        "M_Mon_Initial": "p",
        "T_Tue_Initial": "u",
        "W_Wed_Initial": "s",
        "T_Thu_Initial": "š",
        "F_Fri_Initial": "p",
        "S_Sat_Initial": "s",
        "January": "január",
        "February": "február",
        "March": "marec",
        "April": "apríl",
        "May": "máj",
        "June": "jún",
        "July": "júl",
        "August": "august",
        "September": "september",
        "October": "október",
        "November": "november",
        "December": "december",
        "Jan_Abbr": "I",
        "Feb_Abbr": "II",
        "Mar_Abbr": "III",
        "Apr_Abbr": "IV",
        "May_Abbr": "V",
        "Jun_Abbr": "VI",
        "Jul_Abbr": "VII",
        "Aug_Abbr": "VIII",
        "Sep_Abbr": "IX",
        "Oct_Abbr": "X",
        "Nov_Abbr": "XI",
        "Dec_Abbr": "XII",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d. M. yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "január",
        "/feb(ruary)?/": "február",
        "/mar(ch)?/": "marec",
        "/apr(il)?/": "apríl",
        "/may/": "máj",
        "/jun(e)?/": "jún",
        "/jul(y)?/": "júl",
        "/aug(ust)?/": "august",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "október",
        "/nov(ember)?/": "november",
        "/dec(ember)?/": "december",
        "/^su(n(day)?)?/": "^nedeľa",
        "/^mo(n(day)?)?/": "^pondelok",
        "/^tu(e(s(day)?)?)?/": "^utorok",
        "/^we(d(nesday)?)?/": "^streda",
        "/^th(u(r(s(day)?)?)?)?/": "^štvrtok",
        "/^fr(i(day)?)?/": "^piatok",
        "/^sa(t(urday)?)?/": "^sobota",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sk-SK";
