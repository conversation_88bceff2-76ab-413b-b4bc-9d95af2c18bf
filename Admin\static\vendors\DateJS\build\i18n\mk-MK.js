/* 
 * DateJS Culture String File
 * Country Code: mk-MK
 * Name: Macedonian (Former Yugoslav Republic of Macedonia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["mk-MK"] = {
        "name": "mk-MK",
        "englishName": "Macedonian (Former Yugoslav Republic of Macedonia)",
        "nativeName": "македонски јазик (Македонија)",
        "Sunday": "недела",
        "Monday": "понеделник",
        "Tuesday": "вторник",
        "Wednesday": "среда",
        "Thursday": "четврток",
        "Friday": "петок",
        "Saturday": "сабота",
        "Sun": "нед",
        "Mon": "пон",
        "Tue": "втр",
        "Wed": "срд",
        "Thu": "чет",
        "Fri": "пет",
        "Sat": "саб",
        "Su": "не",
        "Mo": "по",
        "Tu": "вт",
        "We": "ср",
        "Th": "че",
        "Fr": "пе",
        "Sa": "са",
        "S_Sun_Initial": "н",
        "M_Mon_Initial": "п",
        "T_Tue_Initial": "в",
        "W_Wed_Initial": "с",
        "T_Thu_Initial": "ч",
        "F_Fri_Initial": "п",
        "S_Sat_Initial": "с",
        "January": "јануари",
        "February": "февруари",
        "March": "март",
        "April": "април",
        "May": "мај",
        "June": "јуни",
        "July": "јули",
        "August": "август",
        "September": "септември",
        "October": "октомври",
        "November": "ноември",
        "December": "декември",
        "Jan_Abbr": "јан",
        "Feb_Abbr": "фев",
        "Mar_Abbr": "мар",
        "Apr_Abbr": "апр",
        "May_Abbr": "мај",
        "Jun_Abbr": "јун",
        "Jul_Abbr": "јул",
        "Aug_Abbr": "авг",
        "Sep_Abbr": "сеп",
        "Oct_Abbr": "окт",
        "Nov_Abbr": "ное",
        "Dec_Abbr": "дек",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "dddd, dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "јан(уари)?",
        "/feb(ruary)?/": "фев(руари)?",
        "/mar(ch)?/": "мар(т)?",
        "/apr(il)?/": "апр(ил)?",
        "/may/": "мај",
        "/jun(e)?/": "јун(и)?",
        "/jul(y)?/": "јул(и)?",
        "/aug(ust)?/": "авг(уст)?",
        "/sep(t(ember)?)?/": "сеп(тември)?",
        "/oct(ober)?/": "окт(омври)?",
        "/nov(ember)?/": "ное(мври)?",
        "/dec(ember)?/": "дек(ември)?",
        "/^su(n(day)?)?/": "^не(д(ела)?)?",
        "/^mo(n(day)?)?/": "^по(н(еделник)?)?",
        "/^tu(e(s(day)?)?)?/": "^вт(р(рник)?)?",
        "/^we(d(nesday)?)?/": "^ср(д(да)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^че(т(врток)?)?",
        "/^fr(i(day)?)?/": "^пе(т(ок)?)?",
        "/^sa(t(urday)?)?/": "^са(б(ота)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "mk-MK";
