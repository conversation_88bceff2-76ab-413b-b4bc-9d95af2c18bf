/* 
 * DateJS Culture String File
 * Country Code: ar-AE
 * Name: Arabic (U.A.E.)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ar-AE"] = {
        "name": "ar-AE",
        "englishName": "Arabic (U.A.E.)",
        "nativeName": "العربية (الإمارات العربية المتحدة)",
        "Sunday": "الاحد",
        "Monday": "الاثنين",
        "Tuesday": "الثلاثاء",
        "Wednesday": "الاربعاء",
        "Thursday": "الخميس",
        "Friday": "الجمعة",
        "Saturday": "السبت",
        "Sun": "الاحد",
        "Mon": "الاثنين",
        "Tue": "الثلاثاء",
        "Wed": "الاربعاء",
        "Thu": "الخميس",
        "Fri": "الجمعة",
        "Sat": "السبت",
        "Su": "أ",
        "Mo": "ا",
        "Tu": "ث",
        "We": "أ",
        "Th": "خ",
        "Fr": "ج",
        "Sa": "س",
        "S_Sun_Initial": "أ",
        "M_Mon_Initial": "ا",
        "T_Tue_Initial": "ث",
        "W_Wed_Initial": "أ",
        "T_Thu_Initial": "خ",
        "F_Fri_Initial": "ج",
        "S_Sat_Initial": "س",
        "January": "يناير",
        "February": "فبراير",
        "March": "مارس",
        "April": "ابريل",
        "May": "مايو",
        "June": "يونيو",
        "July": "يوليو",
        "August": "اغسطس",
        "September": "سبتمبر",
        "October": "اكتوبر",
        "November": "نوفمبر",
        "December": "ديسمبر",
        "Jan_Abbr": "يناير",
        "Feb_Abbr": "فبراير",
        "Mar_Abbr": "مارس",
        "Apr_Abbr": "ابريل",
        "May_Abbr": "مايو",
        "Jun_Abbr": "يونيو",
        "Jul_Abbr": "يوليو",
        "Aug_Abbr": "اغسطس",
        "Sep_Abbr": "سبتمبر",
        "Oct_Abbr": "اكتوبر",
        "Nov_Abbr": "نوفمبر",
        "Dec_Abbr": "ديسمبر",
        "AM": "ص",
        "PM": "م",
        "firstDayOfWeek": 6,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM, yyyy",
        "h:mm tt": "hh:mm tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM, yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "يناير",
        "/feb(ruary)?/": "فبراير",
        "/mar(ch)?/": "مارس",
        "/apr(il)?/": "ابريل",
        "/may/": "مايو",
        "/jun(e)?/": "يونيو",
        "/jul(y)?/": "يوليو",
        "/aug(ust)?/": "اغسطس",
        "/sep(t(ember)?)?/": "سبتمبر",
        "/oct(ober)?/": "اكتوبر",
        "/nov(ember)?/": "نوفمبر",
        "/dec(ember)?/": "ديسمبر",
        "/^su(n(day)?)?/": "^الاحد",
        "/^mo(n(day)?)?/": "^ا(1)?",
        "/^tu(e(s(day)?)?)?/": "^الثلاثاء",
        "/^we(d(nesday)?)?/": "^الاربعاء",
        "/^th(u(r(s(day)?)?)?)?/": "^الخميس",
        "/^fr(i(day)?)?/": "^الجمعة",
        "/^sa(t(urday)?)?/": "^السبت",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ar-AE";
