/* 
 * DateJS Culture String File
 * Country Code: uk-UA
 * Name: Ukrainian (Ukraine)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["uk-UA"] = {
        "name": "uk-UA",
        "englishName": "Ukrainian (Ukraine)",
        "nativeName": "україньска (Україна)",
        "Sunday": "неділя",
        "Monday": "понеділок",
        "Tuesday": "вівторок",
        "Wednesday": "середа",
        "Thursday": "четвер",
        "Friday": "п'ятниця",
        "Saturday": "субота",
        "Sun": "Нд",
        "Mon": "Пн",
        "Tue": "Вт",
        "Wed": "Ср",
        "Thu": "Чт",
        "Fri": "Пт",
        "Sat": "Сб",
        "Su": "Нд",
        "Mo": "Пн",
        "Tu": "Вт",
        "We": "Ср",
        "Th": "Чт",
        "Fr": "Пт",
        "Sa": "Сб",
        "S_Sun_Initial": "Н",
        "M_Mon_Initial": "П",
        "T_Tue_Initial": "В",
        "W_Wed_Initial": "С",
        "T_Thu_Initial": "Ч",
        "F_Fri_Initial": "П",
        "S_Sat_Initial": "С",
        "January": "Січень",
        "February": "Лютий",
        "March": "Березень",
        "April": "Квітень",
        "May": "Травень",
        "June": "Червень",
        "July": "Липень",
        "August": "Серпень",
        "September": "Вересень",
        "October": "Жовтень",
        "November": "Листопад",
        "December": "Грудень",
        "Jan_Abbr": "Січ",
        "Feb_Abbr": "Лют",
        "Mar_Abbr": "Бер",
        "Apr_Abbr": "Кві",
        "May_Abbr": "Тра",
        "Jun_Abbr": "Чер",
        "Jul_Abbr": "Лип",
        "Aug_Abbr": "Сер",
        "Sep_Abbr": "Вер",
        "Oct_Abbr": "Жов",
        "Nov_Abbr": "Лис",
        "Dec_Abbr": "Гру",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy' р.'",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy' р.' H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy' р.'",
        "/jan(uary)?/": "січ(ень)?",
        "/feb(ruary)?/": "лют(ий)?",
        "/mar(ch)?/": "бер(езень)?",
        "/apr(il)?/": "кві(тень)?",
        "/may/": "тра(вень)?",
        "/jun(e)?/": "чер(вень)?",
        "/jul(y)?/": "лип(ень)?",
        "/aug(ust)?/": "сер(пень)?",
        "/sep(t(ember)?)?/": "вер(есень)?",
        "/oct(ober)?/": "жов(тень)?",
        "/nov(ember)?/": "лис(топад)?",
        "/dec(ember)?/": "гру(день)?",
        "/^su(n(day)?)?/": "^неділя",
        "/^mo(n(day)?)?/": "^понеділок",
        "/^tu(e(s(day)?)?)?/": "^вівторок",
        "/^we(d(nesday)?)?/": "^середа",
        "/^th(u(r(s(day)?)?)?)?/": "^четвер",
        "/^fr(i(day)?)?/": "^п'ятниця",
        "/^sa(t(urday)?)?/": "^субота",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "uk-UA";
