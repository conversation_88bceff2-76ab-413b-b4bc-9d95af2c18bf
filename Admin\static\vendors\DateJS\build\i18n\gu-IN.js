/* 
 * DateJS Culture String File
 * Country Code: gu-IN
 * Name: Gujarati (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["gu-IN"] = {
        "name": "gu-IN",
        "englishName": "Gujarati (India)",
        "nativeName": "ગુજરાતી (ભારત)",
        "Sunday": "રવિવાર",
        "Monday": "સોમવાર",
        "Tuesday": "મંગળવાર",
        "Wednesday": "બુધવાર",
        "Thursday": "ગુરુવાર",
        "Friday": "શુક્રવાર",
        "Saturday": "શનિવાર",
        "Sun": "રવિ",
        "Mon": "સોમ",
        "Tue": "મંગળ",
        "Wed": "બુધ",
        "Thu": "ગુરુ",
        "Fri": "શુક્ર",
        "Sat": "શનિ",
        "Su": "ર",
        "Mo": "સ",
        "Tu": "મ",
        "We": "બ",
        "Th": "ગ",
        "Fr": "શ",
        "Sa": "શ",
        "S_Sun_Initial": "ર",
        "M_Mon_Initial": "સ",
        "T_Tue_Initial": "મ",
        "W_Wed_Initial": "બ",
        "T_Thu_Initial": "ગ",
        "F_Fri_Initial": "શ",
        "S_Sat_Initial": "શ",
        "January": "જાન્યુઆરી",
        "February": "ફેબ્રુઆરી",
        "March": "માર્ચ",
        "April": "એપ્રિલ",
        "May": "મે",
        "June": "જૂન",
        "July": "જુલાઈ",
        "August": "ઑગસ્ટ",
        "September": "સપ્ટેમ્બર",
        "October": "ઑક્ટ્બર",
        "November": "નવેમ્બર",
        "December": "ડિસેમ્બર",
        "Jan_Abbr": "જાન્યુ",
        "Feb_Abbr": "ફેબ્રુ",
        "Mar_Abbr": "માર્ચ",
        "Apr_Abbr": "એપ્રિલ",
        "May_Abbr": "મે",
        "Jun_Abbr": "જૂન",
        "Jul_Abbr": "જુલાઈ",
        "Aug_Abbr": "ઑગસ્ટ",
        "Sep_Abbr": "સપ્ટે",
        "Oct_Abbr": "ઑક્ટો",
        "Nov_Abbr": "નવે",
        "Dec_Abbr": "ડિસે",
        "AM": "પૂર્વ મધ્યાહ્ન",
        "PM": "ઉત્તર મધ્યાહ્ન",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "જાન્યુ(આરી)?",
        "/feb(ruary)?/": "ફેબ્રુ(આરી)?",
        "/mar(ch)?/": "માર્ચ",
        "/apr(il)?/": "એપ્રિલ",
        "/may/": "મે",
        "/jun(e)?/": "જૂન",
        "/jul(y)?/": "જુલાઈ",
        "/aug(ust)?/": "ઑગસ્ટ",
        "/sep(t(ember)?)?/": "સપ્ટે(મ્બર)?",
        "/oct(ober)?/": "ઑક્ટ્બર",
        "/nov(ember)?/": "નવે(મ્બર)?",
        "/dec(ember)?/": "ડિસે(મ્બર)?",
        "/^su(n(day)?)?/": "^ર(વિ(વાર)?)?",
        "/^mo(n(day)?)?/": "^સ(ોમ(વાર)?)?",
        "/^tu(e(s(day)?)?)?/": "^મ(ંગળ(વાર)?)?",
        "/^we(d(nesday)?)?/": "^બ(ુધ(વાર)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ગ(ુરુ(વાર)?)?",
        "/^fr(i(day)?)?/": "^શ(ુક્ર(વાર)?)?",
        "/^sa(t(urday)?)?/": "^શ(નિ(વાર)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "gu-IN";
