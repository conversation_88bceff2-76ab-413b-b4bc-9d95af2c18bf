/* 
 * DateJS Culture String File
 * Country Code: bg-BG
 * Name: Bulgarian (Bulgaria)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["bg-BG"] = {
        "name": "bg-BG",
        "englishName": "Bulgarian (Bulgaria)",
        "nativeName": "български (България)",
        "Sunday": "неделя",
        "Monday": "понеделник",
        "Tuesday": "вторник",
        "Wednesday": "сряда",
        "Thursday": "четвъртък",
        "Friday": "петък",
        "Saturday": "събота",
        "Sun": "Нд",
        "Mon": "Пн",
        "Tue": "Вт",
        "Wed": "Ср",
        "Thu": "Чт",
        "Fri": "Пт",
        "Sat": "Сб",
        "Su": "не",
        "Mo": "по",
        "Tu": "вт",
        "We": "ср",
        "Th": "че",
        "Fr": "пе",
        "Sa": "съ",
        "S_Sun_Initial": "н",
        "M_Mon_Initial": "п",
        "T_Tue_Initial": "в",
        "W_Wed_Initial": "с",
        "T_Thu_Initial": "ч",
        "F_Fri_Initial": "п",
        "S_Sat_Initial": "с",
        "January": "Януари",
        "February": "Февруари",
        "March": "Март",
        "April": "Април",
        "May": "Май",
        "June": "Юни",
        "July": "Юли",
        "August": "Август",
        "September": "Септември",
        "October": "Октомври",
        "November": "Ноември",
        "December": "Декември",
        "Jan_Abbr": "Януари",
        "Feb_Abbr": "Февруари",
        "Mar_Abbr": "Март",
        "Apr_Abbr": "Април",
        "May_Abbr": "Май",
        "Jun_Abbr": "Юни",
        "Jul_Abbr": "Юли",
        "Aug_Abbr": "Август",
        "Sep_Abbr": "Септември",
        "Oct_Abbr": "Октомври",
        "Nov_Abbr": "Ноември",
        "Dec_Abbr": "Декември",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.M.yyyy 'г.'",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy 'г.'",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy 'г.' HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy 'г.'",
        "/jan(uary)?/": "януари",
        "/feb(ruary)?/": "февруари",
        "/mar(ch)?/": "март",
        "/apr(il)?/": "април",
        "/may/": "май",
        "/jun(e)?/": "юни",
        "/jul(y)?/": "юли",
        "/aug(ust)?/": "август",
        "/sep(t(ember)?)?/": "септември",
        "/oct(ober)?/": "октомври",
        "/nov(ember)?/": "ноември",
        "/dec(ember)?/": "декември",
        "/^su(n(day)?)?/": "^не((деля)?)?",
        "/^mo(n(day)?)?/": "^по((неделник)?)?",
        "/^tu(e(s(day)?)?)?/": "^вторник",
        "/^we(d(nesday)?)?/": "^сряда",
        "/^th(u(r(s(day)?)?)?)?/": "^че((твъртък)?)?",
        "/^fr(i(day)?)?/": "^пе((тък)?)?",
        "/^sa(t(urday)?)?/": "^съ((бота)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "bg-BG";
