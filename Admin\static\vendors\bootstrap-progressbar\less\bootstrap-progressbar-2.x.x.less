@progressbarVerticalWidth: @baseLineHeight;
@progressbarFontSize: 12px;

// bootstrap-progressbar global styles
// -----------------------------------

.progress {
  position: relative;
}

.progress .bar {
  position: absolute;
  overflow: hidden;
  line-height: @baseLineHeight;
}

.progress .progressbar-back-text {
  position: absolute;
  width: 100%;
  height: 100%;
  font-size: @progressbarFontSize;
  line-height: @baseLineHeight;
  text-align: center;
}

.progress .progressbar-front-text {
  display: block;
  width: 100%;
  font-size: @progressbarFontSize;
  line-height: @baseLineHeight;
  text-align: center;
}

// bootstrap-progressbar horizontal styles
// ---------------------------------------

.progress.right .bar {
  right: 0;
}

.progress.right .progressbar-front-text {
  position: absolute;
  right: 0;
}

// bootstrap-progressbar vertical styles
// -------------------------------------

.progress.vertical {
  width: @progressbarVerticalWidth;
  height: 100%;
  float: left;
  margin-right: @progressbarVerticalWidth;
  #gradient > .vertical(#f5f5f5, #f9f9f9);
  background-repeat: repeat;
}

.progress.vertical.bottom {
  position: relative;
}

.progress.vertical.bottom .progressbar-front-text {
  position: absolute;
  bottom: 0;
}

.progress.vertical .bar {
  width: 100%;
  height: 0;
  .transition(height .6s ease);
  background-repeat: repeat;
}

.progress.vertical.bottom .bar {
  position: absolute;
  bottom: 0;
}

// Danger (red)
.progress-danger.vertical .bar,
.progress.vertical .bar-danger {
  #gradient > .vertical(#ee5f5b, #c43c35);
  background-repeat: repeat;
}
.progress-danger.progress-striped.vertical .bar,
.progress.progress-striped.vertical .bar-danger {
  #gradient > .striped(#ee5f5b);
}

// Success (green)
.progress-success.vertical .bar,
.progress.vertical .bar-success {
  #gradient > .vertical(#62c462, #57a957);
  background-repeat: repeat;
}
.progress-success.progress-striped.vertical .bar,
.progress.progress-striped.vertical .bar-success {
  #gradient > .striped(#62c462);
}

// Info (teal)
.progress-info.vertical .bar,
.progress.vertical .bar-info {
  #gradient > .vertical(#5bc0de, #339bb9);
  background-repeat: repeat;
}
.progress-info.progress-striped.vertical .bar,
.progress.progress-striped.vertical .bar-info {
  #gradient > .striped(#5bc0de);
}

// Warning (orange)
.progress-warning.vertical .bar,
.progress.vertical .bar-warning {
  #gradient > .vertical(lighten(@orange, 15%), @orange);
  background-repeat: repeat;
}
.progress-warning.progress-striped.vertical .bar,
.progress.progress-striped.vertical .bar-warning {
  #gradient > .striped(lighten(@orange, 15%));
}
