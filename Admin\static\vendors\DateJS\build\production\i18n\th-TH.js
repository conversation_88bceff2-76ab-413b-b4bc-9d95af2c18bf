/* 
 * DateJS Culture String File
 * Country Code: th-TH
 * Name: Thai (Thailand)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["th-TH"] = {
        "name": "th-TH",
        "englishName": "Thai (Thailand)",
        "nativeName": "ไทย (ไทย)",
        "Sunday": "อาทิตย์",
        "Monday": "จันทร์",
        "Tuesday": "อังคาร",
        "Wednesday": "พุธ",
        "Thursday": "พฤหัสบดี",
        "Friday": "ศุกร์",
        "Saturday": "เสาร์",
        "Sun": "อา.",
        "Mon": "จ.",
        "Tue": "อ.",
        "Wed": "พ.",
        "Thu": "พฤ.",
        "Fri": "ศ.",
        "Sat": "ส.",
        "Su": "อ",
        "Mo": "จ",
        "Tu": "อ",
        "We": "พ",
        "Th": "พ",
        "Fr": "ศ",
        "Sa": "ส",
        "S_Sun_Initial": "อ",
        "M_Mon_Initial": "จ",
        "T_Tue_Initial": "อ",
        "W_Wed_Initial": "พ",
        "T_Thu_Initial": "พ",
        "F_Fri_Initial": "ศ",
        "S_Sat_Initial": "ส",
        "January": "มกราคม",
        "February": "กุมภาพันธ์",
        "March": "มีนาคม",
        "April": "เมษายน",
        "May": "พฤษภาคม",
        "June": "มิถุนายน",
        "July": "กรกฎาคม",
        "August": "สิงหาคม",
        "September": "กันยายน",
        "October": "ตุลาคม",
        "November": "พฤศจิกายน",
        "December": "ธันวาคม",
        "Jan_Abbr": "ม.ค.",
        "Feb_Abbr": "ก.พ.",
        "Mar_Abbr": "มี.ค.",
        "Apr_Abbr": "เม.ย.",
        "May_Abbr": "พ.ค.",
        "Jun_Abbr": "มิ.ย.",
        "Jul_Abbr": "ก.ค.",
        "Aug_Abbr": "ส.ค.",
        "Sep_Abbr": "ก.ย.",
        "Oct_Abbr": "ต.ค.",
        "Nov_Abbr": "พ.ย.",
        "Dec_Abbr": "ธ.ค.",
        "AM": "AM",
        "PM": "PM",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2572,
        "mdy": "dmy",
        "M/d/yyyy": "d/M/yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ม(.(กราค)?)?",
        "/feb(ruary)?/": "ก(.(ุมภาพันธ์)?)?",
        "/mar(ch)?/": "มี(.(นาคม)?)?",
        "/apr(il)?/": "เม(.(ษายน)?)?",
        "/may/": "พ(.(ฤษภาคม)?)?",
        "/jun(e)?/": "มิ(.(ถุนายน)?)?",
        "/jul(y)?/": "ก(.(รฎาคม)?)?",
        "/aug(ust)?/": "ส(.(ิงหาคม)?)?",
        "/sep(t(ember)?)?/": "ก(.(ันยายน)?)?",
        "/oct(ober)?/": "ต(.(ุลาคม)?)?",
        "/nov(ember)?/": "พ(.(ฤศจิกายน)?)?",
        "/dec(ember)?/": "ธ(.(ันวาคม)?)?",
        "/^su(n(day)?)?/": "^อ(า(.(ทิตย์)?)?)?",
        "/^mo(n(day)?)?/": "^จ((.(ันทร์)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^อ((.(ังคาร)?)?)?",
        "/^we(d(nesday)?)?/": "^พ((.(ุธ)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^พ(ฤ(.(หัสบดี)?)?)?",
        "/^fr(i(day)?)?/": "^ศ((.(ุกร์)?)?)?",
        "/^sa(t(urday)?)?/": "^ส((.(สาร์)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "th-TH";
