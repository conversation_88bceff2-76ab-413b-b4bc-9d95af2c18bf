/* 
 * DateJS Culture String File
 * Country Code: de-AT
 * Name: German (Austria)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["de-AT"] = {
        "name": "de-AT",
        "englishName": "German (Austria)",
        "nativeName": "Deutsch (Österreich)",
        "Sunday": "Sonntag",
        "Monday": "Montag",
        "Tuesday": "Dienstag",
        "Wednesday": "Mittwoch",
        "Thursday": "Donnerstag",
        "Friday": "Freitag",
        "Saturday": "Samstag",
        "Sun": "Son",
        "Mon": "Mon",
        "Tue": "Die",
        "Wed": "Mit",
        "Thu": "Don",
        "Fri": "Fre",
        "Sat": "Sam",
        "Su": "So",
        "Mo": "Mo",
        "Tu": "Di",
        "We": "Mi",
        "Th": "Do",
        "Fr": "Fr",
        "Sa": "Sa",
        "S_Sun_Initial": "S",
        "M_Mon_Initial": "M",
        "T_Tue_Initial": "D",
        "W_Wed_Initial": "M",
        "T_Thu_Initial": "D",
        "F_Fri_Initial": "F",
        "S_Sat_Initial": "S",
        "January": "Jänner",
        "February": "Februar",
        "March": "März",
        "April": "April",
        "May": "Mai",
        "June": "Juni",
        "July": "Juli",
        "August": "August",
        "September": "September",
        "October": "Oktober",
        "November": "November",
        "December": "Dezember",
        "Jan_Abbr": "J(ä|a)n",
        "Feb_Abbr": "Feb",
        "Mar_Abbr": "(M(a|ä)r|Mrz)",
        "Apr_Abbr": "Apr",
        "May_Abbr": "Mai",
        "Jun_Abbr": "Jun",
        "Jul_Abbr": "Jul",
        "Aug_Abbr": "Aug",
        "Sep_Abbr": "Sep",
        "Oct_Abbr": "Okt",
        "Nov_Abbr": "Nov",
        "Dec_Abbr": "Dez",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "dddd, dd. MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, dd. MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "jän(ner)?",
        "/feb(ruary)?/": "feb(ruar)?",
        "/mar(ch)?/": "mär(z)?",
        "/apr(il)?/": "apr(il)?",
        "/may/": "mai",
        "/jun(e)?/": "jun(i)?",
        "/jul(y)?/": "jul(i)?",
        "/aug(ust)?/": "aug(ust)?",
        "/sep(t(ember)?)?/": "sep(t(ember)?)?",
        "/oct(ober)?/": "okt(ober)?",
        "/nov(ember)?/": "nov(ember)?",
        "/dec(ember)?/": "dez(ember)?",
        "/^su(n(day)?)?/": "^sonntag",
        "/^mo(n(day)?)?/": "^montag",
        "/^tu(e(s(day)?)?)?/": "^dienstag",
        "/^we(d(nesday)?)?/": "^mittwoch",
        "/^th(u(r(s(day)?)?)?)?/": "^donnerstag",
        "/^fr(i(day)?)?/": "^freitag",
        "/^sa(t(urday)?)?/": "^samstag",
        "/^next/": "^nächste(r|s|n)?",
        "/^last|past|prev(ious)?/": "^letzte(r|s|n)?|vergangene(r|s|n)?|vorherige(r|s|n)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|(da)?nach(er)?|von|daher|in)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|(be|zu)?vor|früher)",
        "/^yes(terday)?/": "^gestern",
        "/^t(od(ay)?)?/": "^heute",
        "/^tom(orrow)?/": "^morgen",
        "/^n(ow)?/": "^jetzt|sofort|gleich",
        "/^ms|milli(second)?s?/": "^ms|milli(sekunde(n)?)?",
        "/^sec(ond)?s?/": "^sek(unde(n)?)?",
        "/^mn|min(ute)?s?/": "^mn|min(ute(n)?)?",
        "/^h(our)?s?/": "^h|st(d|unde(n)?)?",
        "/^w(eek)?s?/": "^w(oche(n)?)?",
        "/^m(onth)?s?/": "^m(onat(e)?)?",
        "/^d(ay)?s?/": "^d|t(ag(en)?)?",
        "/^y(ear)?s?/": "^y|j(ahr(en)?)?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "de-AT";
