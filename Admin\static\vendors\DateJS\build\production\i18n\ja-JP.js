/* 
 * DateJS Culture String File
 * Country Code: ja-JP
 * Name: Japanese (Japan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ja-<PERSON>"] = {
        "name": "ja-<PERSON>",
        "englishName": "Japanese (Japan)",
        "nativeName": "日本語 (日本)",
        "Sunday": "日曜日",
        "Monday": "月曜日",
        "Tuesday": "火曜日",
        "Wednesday": "水曜日",
        "Thursday": "木曜日",
        "Friday": "金曜日",
        "Saturday": "土曜日",
        "Sun": "日",
        "Mon": "月",
        "Tue": "火",
        "Wed": "水",
        "Thu": "木",
        "Fri": "金",
        "Sat": "土",
        "Su": "日",
        "Mo": "月",
        "Tu": "火",
        "We": "水",
        "Th": "木",
        "Fr": "金",
        "Sa": "土",
        "S_Sun_Initial": "日",
        "M_Mon_Initial": "月",
        "T_Tue_Initial": "火",
        "W_Wed_Initial": "水",
        "T_Thu_Initial": "木",
        "F_Fri_Initial": "金",
        "S_Sat_Initial": "土",
        "January": "1月",
        "February": "2月",
        "March": "3月",
        "April": "4月",
        "May": "5月",
        "June": "6月",
        "July": "7月",
        "August": "8月",
        "September": "9月",
        "October": "10月",
        "November": "11月",
        "December": "12月",
        "Jan_Abbr": "1",
        "Feb_Abbr": "2",
        "Mar_Abbr": "3",
        "Apr_Abbr": "4",
        "May_Abbr": "5",
        "Jun_Abbr": "6",
        "Jul_Abbr": "7",
        "Aug_Abbr": "8",
        "Sep_Abbr": "9",
        "Oct_Abbr": "10",
        "Nov_Abbr": "11",
        "Dec_Abbr": "12",
        "AM": "午前",
        "PM": "午後",
        "firstDayOfWeek": 0,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy/MM/dd",
        "dddd, MMMM dd, yyyy": "yyyy'年'M'月'd'日'",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy'年'M'月'd'日' H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "M'月'd'日'",
        "MMMM, yyyy": "yyyy'年'M'月'",
        "/jan(uary)?/": "1(月)?",
        "/feb(ruary)?/": "2(月)?",
        "/mar(ch)?/": "3(月)?",
        "/apr(il)?/": "4(月)?",
        "/may/": "5(月)?",
        "/jun(e)?/": "6(月)?",
        "/jul(y)?/": "7(月)?",
        "/aug(ust)?/": "8(月)?",
        "/sep(t(ember)?)?/": "9(月)?",
        "/oct(ober)?/": "10(月)?",
        "/nov(ember)?/": "11(月)?",
        "/dec(ember)?/": "12(月)?",
        "/^su(n(day)?)?/": "^日曜日",
        "/^mo(n(day)?)?/": "^月曜日",
        "/^tu(e(s(day)?)?)?/": "^火曜日",
        "/^we(d(nesday)?)?/": "^水曜日",
        "/^th(u(r(s(day)?)?)?)?/": "^木曜日",
        "/^fr(i(day)?)?/": "^金曜日",
        "/^sa(t(urday)?)?/": "^土曜日",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ja-JP";
