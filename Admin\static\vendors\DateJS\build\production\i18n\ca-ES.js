/* 
 * DateJS Culture String File
 * Country Code: ca-ES
 * Name: Catalan (Catalan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ca-ES"] = {
        "name": "ca-ES",
        "englishName": "Catalan (Catalan)",
        "nativeName": "català (català)",
        "Sunday": "diumenge",
        "Monday": "dilluns",
        "Tuesday": "dimarts",
        "Wednesday": "dimecres",
        "Thursday": "dijous",
        "Friday": "divendres",
        "Saturday": "dissabte",
        "Sun": "dg.",
        "Mon": "dl.",
        "Tue": "dt.",
        "Wed": "dc.",
        "Thu": "dj.",
        "Fri": "dv.",
        "Sat": "ds.",
        "Su": "dg",
        "Mo": "dl",
        "Tu": "dt",
        "We": "dc",
        "Th": "dj",
        "Fr": "dv",
        "Sa": "ds",
        "S_Sun_Initial": "d",
        "M_Mon_Initial": "d",
        "T_Tue_Initial": "d",
        "W_Wed_Initial": "d",
        "T_Thu_Initial": "d",
        "F_Fri_Initial": "d",
        "S_Sat_Initial": "d",
        "January": "gener",
        "February": "febrer",
        "March": "març",
        "April": "abril",
        "May": "maig",
        "June": "juny",
        "July": "juliol",
        "August": "agost",
        "September": "setembre",
        "October": "octubre",
        "November": "novembre",
        "December": "desembre",
        "Jan_Abbr": "gen",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "març",
        "Apr_Abbr": "abr",
        "May_Abbr": "maig",
        "Jun_Abbr": "juny",
        "Jul_Abbr": "jul",
        "Aug_Abbr": "ag",
        "Sep_Abbr": "set",
        "Oct_Abbr": "oct",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "des",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d' / 'MMMM' / 'yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d' / 'MMMM' / 'yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM' / 'yyyy",
        "/jan(uary)?/": "gen(er)?",
        "/feb(ruary)?/": "feb(rer)?",
        "/mar(ch)?/": "març",
        "/apr(il)?/": "abr(il)?",
        "/may/": "maig",
        "/jun(e)?/": "juny",
        "/jul(y)?/": "jul(iol)?",
        "/aug(ust)?/": "ag(ost)?",
        "/sep(t(ember)?)?/": "set(embre)?",
        "/oct(ober)?/": "oct(ubre)?",
        "/nov(ember)?/": "nov(embre)?",
        "/dec(ember)?/": "des(embre)?",
        "/^su(n(day)?)?/": "^dg((.(umenge)?)?)?",
        "/^mo(n(day)?)?/": "^dl((.(lluns)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^dt((.(marts)?)?)?",
        "/^we(d(nesday)?)?/": "^dc((.(mecres)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^dj((.(jous)?)?)?",
        "/^fr(i(day)?)?/": "^dv((.(vendres)?)?)?",
        "/^sa(t(urday)?)?/": "^ds((.(ssabte)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ca-ES";
