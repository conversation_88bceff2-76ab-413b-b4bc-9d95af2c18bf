<!doctype html>
<html lang="zh">
 <head>
	<meta charset="utf-8">
	<title>JQuery信息提示框插件 jquery.toast.js 的使用</title>

	<link href="jquery/toast/1.3.2/jquery.toast.css" rel="stylesheet">

     <script src="jquery/jquery.min.js"></script>
	<script src="jquery/toast/1.3.2/jquery.toast.min.js"></script>
 </head>

 <body>
	 <button class="btn btn-success" name="success"id="simple"> 简单文本的消息提示框 </button>
	 <button class="btn btn-success" name="success"id="success"> 成功 </button>
     <button class="btn btn-info" name="info"id="info">提示</button>
     <button class="btn btn-warning" name="warning"id="warning">警告</button>
     <button class="btn btn-danger" name="error"id="error">错误</button>

	<script>
		 $(function(){
			$("#simple").click(function(){
				$.toast('Here you can put the text of the toast');
			})

			$("#success").click(function(){
				showMsg("成功样式！", "success", 2000);
			})
			$("#info").click(function(){
				showMsg("提示样式！", "info", 2000);
			});

			$("#warning").click(function(){
				showMsg("警告样式！", "warning", 2000);
			})
			$("#error").click(function(){
				showMsg("错误样式！", "error", 2000);
			})

    });

	 /**
     * 提示框
     * @param text
     * @param icon
     * @param hideAfter
     */
    function showMsg(text, icon, hideAfter) {
        if (heading == undefined) {
            var heading = "提示";
        }
        $.toast({
            text: text,//消息提示框的内容。
            heading: heading,//消息提示框的标题。
            icon: icon,//消息提示框的图标样式。
            showHideTransition: 'fade',//消息提示框的动画效果。可取值：plain，fade，slide。
            allowToastClose: true,//是否显示关闭按钮。(true 显示，false 不显示)
            hideAfter: hideAfter,//设置为false则消息提示框不自动关闭.设置为一个数值则在指定的毫秒之后自动关闭消息提框
            stack: 1,//消息栈。同时允许的提示框数量
            position: 'top-center',//消息提示框的位置：bottom-left, bottom-right,bottom-center,top-left,top-right,top-center,mid-center。
            textAlign: 'left',//文本对齐：left, right, center。
            loader: true,//是否显示加载条
			//bgColor: '#FF1356',//背景颜色。
			//textColor: '#eee',//文字颜色。
            loaderBg: '#ffffff',//加载条的背景颜色。

			beforeShow: function(){

			},

			afterShown: function () {

			},

			beforeHide: function () {

			},

			afterHidden: function () {

			}

			/*toast事件
			beforeShow 会在toast即将出现之前触发
			afterShown 会在toast出现后触发
			beforeHide 会在toast藏起来之前触发
			afterHidden 会在toast藏起来后被触发
			*/
        });
    }

	</script>
 </body>
</html>