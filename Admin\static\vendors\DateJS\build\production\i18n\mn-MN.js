/* 
 * DateJS Culture String File
 * Country Code: mn-MN
 * Name: Mongolian (Cyrillic, Mongolia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["mn-MN"] = {
        "name": "mn-MN",
        "englishName": "Mongolian (Cyrillic, Mongolia)",
        "nativeName": "Монгол хэл (Монгол улс)",
        "Sunday": "Ням",
        "Monday": "Даваа",
        "Tuesday": "Мягмар",
        "Wednesday": "Лхагва",
        "Thursday": "Пүрэв",
        "Friday": "Баасан",
        "Saturday": "Бямба",
        "Sun": "Ня",
        "Mon": "Да",
        "Tue": "Мя",
        "Wed": "Лх",
        "Thu": "Пү",
        "Fri": "Ба",
        "Sat": "Бя",
        "Su": "Ня",
        "Mo": "Да",
        "Tu": "Мя",
        "We": "Лх",
        "Th": "Пү",
        "Fr": "Ба",
        "Sa": "Бя",
        "S_Sun_Initial": "Н",
        "M_Mon_Initial": "Д",
        "T_Tue_Initial": "М",
        "W_Wed_Initial": "Л",
        "T_Thu_Initial": "П",
        "F_Fri_Initial": "Б",
        "S_Sat_Initial": "Б",
        "January": "1 дүгээр сар",
        "February": "2 дугаар сар",
        "March": "3 дугаар сар",
        "April": "4 дүгээр сар",
        "May": "5 дугаар сар",
        "June": "6 дугаар сар",
        "July": "7 дугаар сар",
        "August": "8 дугаар сар",
        "September": "9 дүгээр сар",
        "October": "10 дугаар сар",
        "November": "11 дүгээр сар",
        "December": "12 дугаар сар",
        "Jan_Abbr": "I",
        "Feb_Abbr": "II",
        "Mar_Abbr": "III",
        "Apr_Abbr": "IV",
        "May_Abbr": "V",
        "Jun_Abbr": "VI",
        "Jul_Abbr": "VII",
        "Aug_Abbr": "VШ",
        "Sep_Abbr": "IX",
        "Oct_Abbr": "X",
        "Nov_Abbr": "XI",
        "Dec_Abbr": "XII",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yy.MM.dd",
        "dddd, MMMM dd, yyyy": "yyyy 'оны' MMMM d",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy 'оны' MMMM d H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "yyyy 'он' MMMM",
        "/jan(uary)?/": "1 дүгээр сар",
        "/feb(ruary)?/": "2 дугаар сар",
        "/mar(ch)?/": "3 дугаар сар",
        "/apr(il)?/": "4 дүгээр сар",
        "/may/": "5 дугаар сар",
        "/jun(e)?/": "6 дугаар сар",
        "/jul(y)?/": "7 дугаар сар",
        "/aug(ust)?/": "8 дугаар сар",
        "/sep(t(ember)?)?/": "9 дүгээр сар",
        "/oct(ober)?/": "10 дугаар сар",
        "/nov(ember)?/": "11 дүгээр сар",
        "/dec(ember)?/": "12 дугаар сар",
        "/^su(n(day)?)?/": "^ням",
        "/^mo(n(day)?)?/": "^даваа",
        "/^tu(e(s(day)?)?)?/": "^мягмар",
        "/^we(d(nesday)?)?/": "^лхагва",
        "/^th(u(r(s(day)?)?)?)?/": "^пүрэв",
        "/^fr(i(day)?)?/": "^баасан",
        "/^sa(t(urday)?)?/": "^бямба",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "mn-MN";
