/* 
 * DateJS Culture String File
 * Country Code: sma-SE
 * Name: <PERSON> (Southern) (Sweden)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sma-SE"] = {
        "name": "sma-SE",
        "englishName": "<PERSON> (Southern) (Sweden)",
        "nativeName": "åarjelsaemiengiele (Sveerje)",
        "Sunday": "aejlege",
        "Monday": "måanta",
        "Tuesday": "dæjsta",
        "Wednesday": "gaskevåhkoe",
        "Thursday": "duarsta",
        "Friday": "bearja<PERSON><PERSON>",
        "Saturday": "laavvard<PERSON><PERSON>",
        "Sun": "aej",
        "Mon": "måa",
        "Tue": "dæj",
        "Wed": "gask",
        "Thu": "duar",
        "Fri": "bearj",
        "Sat": "laav",
        "Su": "aej",
        "<PERSON>": "måa",
        "Tu": "dæj",
        "We": "gask",
        "Th": "duar",
        "Fr": "bearj",
        "Sa": "laav",
        "S_Sun_Initial": "a",
        "M_Mon_Initial": "m",
        "T_Tue_Initial": "d",
        "W_Wed_Initial": "g",
        "T_Thu_Initial": "d",
        "F_Fri_Initial": "b",
        "S_Sat_Initial": "l",
        "January": "tsïengele",
        "February": "goevte",
        "March": "njoktje",
        "April": "voerhtje",
        "May": "suehpede",
        "June": "ruffie",
        "July": "snjaltje",
        "August": "mïetske",
        "September": "skïerede",
        "October": "golke",
        "November": "rahka",
        "December": "goeve",
        "Jan_Abbr": "tsïen",
        "Feb_Abbr": "goevt",
        "Mar_Abbr": "njok",
        "Apr_Abbr": "voer",
        "May_Abbr": "sueh",
        "Jun_Abbr": "ruff",
        "Jul_Abbr": "snja",
        "Aug_Abbr": "mïet",
        "Sep_Abbr": "skïer",
        "Oct_Abbr": "golk",
        "Nov_Abbr": "rahk",
        "Dec_Abbr": "goev",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "ymd",
        "M/d/yyyy": "yyyy-MM-dd",
        "dddd, MMMM dd, yyyy": "MMMM d'. b. 'yyyy",
        "h:mm tt": "HH:mm:ss",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "MMMM d'. b. 'yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "tsïen(gele)?",
        "/feb(ruary)?/": "goevt(e)?",
        "/mar(ch)?/": "njok(tje)?",
        "/apr(il)?/": "voer(htje)?",
        "/may/": "sueh(pede)?",
        "/jun(e)?/": "ruff(ie)?",
        "/jul(y)?/": "snja(ltje)?",
        "/aug(ust)?/": "mïet(ske)?",
        "/sep(t(ember)?)?/": "skïer(ede)?",
        "/oct(ober)?/": "golk(e)?",
        "/nov(ember)?/": "rahk(a)?",
        "/dec(ember)?/": "goev(e)?",
        "/^su(n(day)?)?/": "^aejlege",
        "/^mo(n(day)?)?/": "^måanta",
        "/^tu(e(s(day)?)?)?/": "^dæjsta",
        "/^we(d(nesday)?)?/": "^gaskevåhkoe",
        "/^th(u(r(s(day)?)?)?)?/": "^duarsta",
        "/^fr(i(day)?)?/": "^bearjadahke",
        "/^sa(t(urday)?)?/": "^laavvardahke",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sma-SE";
