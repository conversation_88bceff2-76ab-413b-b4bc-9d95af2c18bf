// Cropper
// =========================

.cropper-container {
  position: relative;
  font-size: 0;
  line-height: 0;
  direction: ltr !important; // Ignore global rtl
  touch-action: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;

  img {
    // Avoid margin top issue (Occur only when margin-top <= -height)
    display: block;

    // https://developer.mozilla.org/en-US/docs/Web/CSS/image-orientation
    image-orientation: 0deg !important;

    min-width: 0 !important;
    min-height: 0 !important;
    max-width: none !important;
    max-height: none !important;
    @include square(100%);
  }
}

.cropper-wrap-box,
.cropper-canvas,
.cropper-drag-box,
.cropper-crop-box,
.cropper-modal {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cropper-wrap-box {
  overflow: hidden;
}

.cropper-drag-box {
  background-color: #fff;
  @include opacity(0);
}

.cropper-modal {
  background-color: #000;
  @include opacity(.5);
}

.cropper-view-box {
  display: block;
  overflow: hidden;
  outline: 1px solid $color-brand;
  outline-color: rgba($color-brand, .75);
  @include square(100%);
}

.cropper-dashed {
  position: absolute;
  display: block;
  border: 0 dashed #eee;
  @include opacity(.5);

  &.dashed-h {
    top: percentage(1 / 3);
    left: 0;
    border-top-width: 1px;
    border-bottom-width: 1px;
    @include size(100%, percentage(1 / 3));
  }

  &.dashed-v {
    top: 0;
    left: percentage(1 / 3);
    border-right-width: 1px;
    border-left-width: 1px;
    @include size(percentage(1 / 3), 100%);
  }
}

.cropper-center {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 0;
  height: 0;
  @include opacity(.75);

  &:before,
  &:after {
    position: absolute;
    display: block;
    background-color: #eee;
    content: " ";
  }

  &:before {
    top: 0;
    left: -3px;
    width: 7px;
    height: 1px;
  }

  &:after {
    top: -3px;
    left: 0;
    width: 1px;
    height: 7px;
  }
}

.cropper-face,
.cropper-line,
.cropper-point {
  position: absolute;
  display: block;
  @include square(100%);
  @include opacity(.1);
}

.cropper-face {
  top: 0;
  left: 0;
  background-color: #fff;
}

.cropper-line {
  background-color: $color-brand;

  &.line-e {
    top: 0;
    right: -3px;
    width: 5px;
    cursor: e-resize;
  }

  &.line-n {
    top: -3px;
    left: 0;
    height: 5px;
    cursor: n-resize;
  }

  &.line-w {
    top: 0;
    left: -3px;
    width: 5px;
    cursor: w-resize;
  }

  &.line-s {
    bottom: -3px;
    left: 0;
    height: 5px;
    cursor: s-resize;
  }
}

.cropper-point {
  background-color: $color-brand;
  @include square(5px);
  @include opacity(.75);

  &.point-e {
    top: 50%;
    right: -3px;
    margin-top: -3px;
    cursor: e-resize;
  }

  &.point-n {
    top: -3px;
    left: 50%;
    margin-left: -3px;
    cursor: n-resize;
  }

  &.point-w {
    top: 50%;
    left: -3px;
    margin-top: -3px;
    cursor: w-resize;
  }

  &.point-s {
    bottom: -3px;
    left: 50%;
    margin-left: -3px;
    cursor: s-resize;
  }

  &.point-ne {
    top: -3px;
    right: -3px;
    cursor: ne-resize;
  }

  &.point-nw {
    top: -3px;
    left: -3px;
    cursor: nw-resize;
  }

  &.point-sw {
    bottom: -3px;
    left: -3px;
    cursor: sw-resize;
  }

  &.point-se {
    right: -3px;
    bottom: -3px;
    cursor: se-resize;
    @include square(20px);
    @include opacity(1);
  }

  &.point-se:before {
    position: absolute;
    right: -50%;
    bottom: -50%;
    display: block;
    content: " ";
    background-color: $color-brand;
    @include square(200%);
    @include opacity(0);
  }

  @media (min-width: $screen-sm) {
    &.point-se {
      @include square(15px);
    }
  }

  @media (min-width: $screen-md) {
    &.point-se {
      @include square(10px);
    }
  }

  @media (min-width: $screen-lg) {
    &.point-se {
      @include square(5px);
      @include opacity(.75);
    }
  }
}
