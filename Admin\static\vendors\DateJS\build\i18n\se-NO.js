/* 
 * DateJS Culture String File
 * Country Code: se-NO
 * Name: <PERSON> (Northern) (Norway)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["se-NO"] = {
        "name": "se-NO",
        "englishName": "<PERSON> (Northern) (Norway)",
        "nativeName": "davvisámegiella (Norga)",
        "Sunday": "sotnabeaivi",
        "Monday": "vuossárga",
        "Tuesday": "maŋŋebárga",
        "Wednesday": "gaskavahkku",
        "Thursday": "duorastat",
        "Friday": "bearjadat",
        "Saturday": "lávvardat",
        "Sun": "sotn",
        "Mon": "vuos",
        "Tue": "maŋ",
        "Wed": "gask",
        "Thu": "duor",
        "Fri": "bear",
        "Sat": "láv",
        "Su": "sotn",
        "Mo": "vuos",
        "Tu": "maŋ",
        "We": "gask",
        "Th": "duor",
        "Fr": "bear",
        "Sa": "láv",
        "S_Sun_Initial": "s",
        "M_Mon_Initial": "v",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "g",
        "T_Thu_Initial": "d",
        "F_Fri_Initial": "b",
        "S_Sat_Initial": "l",
        "January": "ođđajagemánnu",
        "February": "guovvamánnu",
        "March": "njukčamánnu",
        "April": "cuoŋománnu",
        "May": "miessemánnu",
        "June": "geassemánnu",
        "July": "suoidnemánnu",
        "August": "borgemánnu",
        "September": "čakčamánnu",
        "October": "golggotmánnu",
        "November": "skábmamánnu",
        "December": "juovlamánnu",
        "Jan_Abbr": "ođđj",
        "Feb_Abbr": "guov",
        "Mar_Abbr": "njuk",
        "Apr_Abbr": "cuo",
        "May_Abbr": "mies",
        "Jun_Abbr": "geas",
        "Jul_Abbr": "suoi",
        "Aug_Abbr": "borg",
        "Sep_Abbr": "čakč",
        "Oct_Abbr": "golg",
        "Nov_Abbr": "skáb",
        "Dec_Abbr": "juov",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "MMMM d'. b. 'yyyy",
        "h:mm tt": "HH:mm:ss",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "MMMM d'. b. 'yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ođđajagemánnu",
        "/feb(ruary)?/": "guov(vamánnu)?",
        "/mar(ch)?/": "njuk(čamánnu)?",
        "/apr(il)?/": "cuo(ŋománnu)?",
        "/may/": "mies(semánnu)?",
        "/jun(e)?/": "geas(semánnu)?",
        "/jul(y)?/": "suoi(dnemánnu)?",
        "/aug(ust)?/": "borg(emánnu)?",
        "/sep(t(ember)?)?/": "čakč(amánnu)?",
        "/oct(ober)?/": "golg(gotmánnu)?",
        "/nov(ember)?/": "skáb(mamánnu)?",
        "/dec(ember)?/": "juov(lamánnu)?",
        "/^su(n(day)?)?/": "^sotnabeaivi",
        "/^mo(n(day)?)?/": "^vuossárga",
        "/^tu(e(s(day)?)?)?/": "^maŋŋebárga",
        "/^we(d(nesday)?)?/": "^gaskavahkku",
        "/^th(u(r(s(day)?)?)?)?/": "^duorastat",
        "/^fr(i(day)?)?/": "^bearjadat",
        "/^sa(t(urday)?)?/": "^lávvardat",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "se-NO";
