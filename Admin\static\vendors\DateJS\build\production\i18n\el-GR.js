/* 
 * DateJS Culture String File
 * Country Code: el-GR
 * Name: Greek (Greece)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["el-GR"] = {
        "name": "el-GR",
        "englishName": "Greek (Greece)",
        "nativeName": "ελληνικά (Ελλάδα)",
        "Sunday": "Κυριακή",
        "Monday": "Δευτέρα",
        "Tuesday": "Τρίτη",
        "Wednesday": "Τετάρτη",
        "Thursday": "Πέμπτη",
        "Friday": "Παρασκευή",
        "Saturday": "Σάββατο",
        "Sun": "Κυρ",
        "Mon": "Δευ",
        "Tue": "Τρι",
        "Wed": "Τετ",
        "Thu": "Πεμ",
        "Fri": "Παρ",
        "Sat": "Σαβ",
        "Su": "Κυ",
        "Mo": "Δε",
        "Tu": "Τρ",
        "We": "Τε",
        "Th": "Πε",
        "Fr": "Πα",
        "Sa": "Σά",
        "S_Sun_Initial": "Κ",
        "M_Mon_Initial": "Δ",
        "T_Tue_Initial": "Τ",
        "W_Wed_Initial": "Τ",
        "T_Thu_Initial": "Π",
        "F_Fri_Initial": "Π",
        "S_Sat_Initial": "Σ",
        "January": "Ιανουάριος",
        "February": "Φεβρουάριος",
        "March": "Μάρτιος",
        "April": "Απρίλιος",
        "May": "Μάιος",
        "June": "Ιούνιος",
        "July": "Ιούλιος",
        "August": "Αύγουστος",
        "September": "Σεπτέμβριος",
        "October": "Οκτώβριος",
        "November": "Νοέμβριος",
        "December": "Δεκέμβριος",
        "Jan_Abbr": "Ιαν",
        "Feb_Abbr": "Φεβ",
        "Mar_Abbr": "Μαρ",
        "Apr_Abbr": "Απρ",
        "May_Abbr": "Μαϊ",
        "Jun_Abbr": "Ιουν",
        "Jul_Abbr": "Ιουλ",
        "Aug_Abbr": "Αυγ",
        "Sep_Abbr": "Σεπ",
        "Oct_Abbr": "Οκτ",
        "Nov_Abbr": "Νοε",
        "Dec_Abbr": "Δεκ",
        "AM": "πμ",
        "PM": "μμ",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d/M/yyyy",
        "dddd, MMMM dd, yyyy": "dddd, d MMMM yyyy",
        "h:mm tt": "h:mm tt",
        "h:mm:ss tt": "h:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, d MMMM yyyy h:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ιαν(ουάριος)?",
        "/feb(ruary)?/": "φεβ(ρουάριος)?",
        "/mar(ch)?/": "μάρτιος",
        "/apr(il)?/": "απρ(ίλιος)?",
        "/may/": "μάιος",
        "/jun(e)?/": "ιούνιος",
        "/jul(y)?/": "ιούλιος",
        "/aug(ust)?/": "αύγουστος",
        "/sep(t(ember)?)?/": "σεπ(τέμβριος)?",
        "/oct(ober)?/": "οκτ(ώβριος)?",
        "/nov(ember)?/": "νοέμβριος",
        "/dec(ember)?/": "δεκ(έμβριος)?",
        "/^su(n(day)?)?/": "^κυ(ρ(ιακή)?)?",
        "/^mo(n(day)?)?/": "^δε(υ(τέρα)?)?",
        "/^tu(e(s(day)?)?)?/": "^τρ(ι(τη)?)?",
        "/^we(d(nesday)?)?/": "^τε(τ(άρτη)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^πε(μ(πτη)?)?",
        "/^fr(i(day)?)?/": "^πα(ρ(ασκευή)?)?",
        "/^sa(t(urday)?)?/": "^σά(β(βατο)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "el-GR";
