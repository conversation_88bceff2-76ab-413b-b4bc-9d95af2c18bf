/* 
 * DateJS Culture String File
 * Country Code: kok-IN
 * Name: <PERSON><PERSON><PERSON> (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["kok-IN"] = {
        "name": "kok-IN",
        "englishName": "Konkani (India)",
        "nativeName": "कोंकणी (भारत)",
        "Sunday": "आयतार",
        "Monday": "सोमार",
        "Tuesday": "मंगळार",
        "Wednesday": "बुधवार",
        "Thursday": "बिरेस्तार",
        "Friday": "सुक्रार",
        "Saturday": "शेनवार",
        "Sun": "आय.",
        "Mon": "सोम.",
        "Tue": "मंगळ.",
        "Wed": "बुध.",
        "Thu": "बिरे.",
        "Fri": "सुक्र.",
        "Sat": "शेन.",
        "Su": "आ",
        "Mo": "स",
        "Tu": "म",
        "We": "ब",
        "Th": "ब",
        "Fr": "स",
        "Sa": "श",
        "S_Sun_Initial": "आ",
        "M_Mon_Initial": "स",
        "T_Tue_Initial": "म",
        "W_Wed_Initial": "ब",
        "T_Thu_Initial": "ब",
        "F_Fri_Initial": "स",
        "S_Sat_Initial": "श",
        "January": "जानेवारी",
        "February": "फेब्रुवारी",
        "March": "मार्च",
        "April": "एप्रिल",
        "May": "मे",
        "June": "जून",
        "July": "जुलै",
        "August": "ऑगस्ट",
        "September": "सप्टेंबर",
        "October": "ऑक्टोबर",
        "November": "नोवेम्बर",
        "December": "डिसेंबर",
        "Jan_Abbr": "जानेवारी",
        "Feb_Abbr": "फेब्रुवारी",
        "Mar_Abbr": "मार्च",
        "Apr_Abbr": "एप्रिल",
        "May_Abbr": "मे",
        "Jun_Abbr": "जून",
        "Jul_Abbr": "जुलै",
        "Aug_Abbr": "ऑगस्ट",
        "Sep_Abbr": "सप्टेंबर",
        "Oct_Abbr": "ऑक्टोबर",
        "Nov_Abbr": "नोवेम्बर",
        "Dec_Abbr": "डिसेंबर",
        "AM": "म.पू.",
        "PM": "म.नं.",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "जानेवारी",
        "/feb(ruary)?/": "फेब्रुवारी",
        "/mar(ch)?/": "मार्च",
        "/apr(il)?/": "एप्रिल",
        "/may/": "मे",
        "/jun(e)?/": "जून",
        "/jul(y)?/": "जुलै",
        "/aug(ust)?/": "ऑगस्ट",
        "/sep(t(ember)?)?/": "सप्टेंबर",
        "/oct(ober)?/": "ऑक्टोबर",
        "/nov(ember)?/": "नोवेम्बर",
        "/dec(ember)?/": "डिसेंबर",
        "/^su(n(day)?)?/": "^आ(य(.(तार)?)?)?",
        "/^mo(n(day)?)?/": "^स(ोम(.(ार)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^म(ंगळ(.(ार)?)?)?",
        "/^we(d(nesday)?)?/": "^ब(ुध(.(वार)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ब(िरे(.(स्तार)?)?)?",
        "/^fr(i(day)?)?/": "^स(ुक्र(.(ार)?)?)?",
        "/^sa(t(urday)?)?/": "^श(ेन(.(वार)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "kok-IN";
