{"name": "cropper", "description": "A simple jQuery image cropping plugin.", "main": ["dist/cropper.js", "dist/cropper.css"], "keywords": ["image", "crop", "cropper", "cropping", "move", "zoom", "rotate", "scale", "j<PERSON>y", "plugin", "html", "css", "javascript", "front-end", "web", "development"], "homepage": "https://github.com/fengyuanchen/cropper", "authors": ["<PERSON><PERSON> Chen"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "tests", "test", "examples", "assets", "demo", "docs", "gulpfile.js", "CONTRIBUTING.md", "FAQ.md"], "dependencies": {"jquery": ">= 1.9.1"}, "devDependencies": {"bootstrap": "~3.3.6", "fontawesome": "~4.6.1", "html5-boilerplate": "~5.3.0", "jquery": "~1.12.3", "qunit": "~1.22.0"}, "version": "2.3.1", "_release": "2.3.1", "_resolution": {"type": "version", "tag": "v2.3.1", "commit": "91547592292e2f296e40bbab4881e41013b80d27"}, "_source": "https://github.com/fengyuanchen/cropper.git", "_target": "^2.3.0", "_originalSource": "cropper"}