/* 
 * DateJS Culture String File
 * Country Code: ta-IN
 * Name: Tamil (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ta-IN"] = {
        "name": "ta-IN",
        "englishName": "Tamil (India)",
        "nativeName": "தமிழ் (இந்தியா)",
        "Sunday": "ஞாயிறு",
        "Monday": "திங்கள்",
        "Tuesday": "செவ்வாய்",
        "Wednesday": "புதன்",
        "Thursday": "வியாழன்",
        "Friday": "வெள்ளி",
        "Saturday": "சனி",
        "Sun": "ஞா",
        "Mon": "தி",
        "Tue": "செ",
        "Wed": "பு",
        "Thu": "வி",
        "Fri": "வெ",
        "Sat": "ச",
        "Su": "ஞ",
        "Mo": "த",
        "Tu": "ச",
        "We": "ப",
        "Th": "வ",
        "Fr": "வ",
        "Sa": "ச",
        "S_Sun_Initial": "ஞ",
        "M_Mon_Initial": "த",
        "T_Tue_Initial": "ச",
        "W_Wed_Initial": "ப",
        "T_Thu_Initial": "வ",
        "F_Fri_Initial": "வ",
        "S_Sat_Initial": "ச",
        "January": "ஜனவரி",
        "February": "பெப்ரவரி",
        "March": "மார்ச்",
        "April": "ஏப்ரல்",
        "May": "மே",
        "June": "ஜூன்",
        "July": "ஜூலை",
        "August": "ஆகஸ்ட்",
        "September": "செப்டம்பர்",
        "October": "அக்டோபர்",
        "November": "நவம்பர்",
        "December": "டிசம்பர்",
        "Jan_Abbr": "ஜன.",
        "Feb_Abbr": "பெப்.",
        "Mar_Abbr": "மார்.",
        "Apr_Abbr": "ஏப்.",
        "May_Abbr": "மே",
        "Jun_Abbr": "ஜூன்",
        "Jul_Abbr": "ஜூலை",
        "Aug_Abbr": "ஆக.",
        "Sep_Abbr": "செப்.",
        "Oct_Abbr": "அக்.",
        "Nov_Abbr": "நவ.",
        "Dec_Abbr": "டிச.",
        "AM": "காலை",
        "PM": "மாலை",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "ஜன(.(வரி)?)?",
        "/feb(ruary)?/": "பெப்(.(ரவரி)?)?",
        "/mar(ch)?/": "மார்(.(ச்)?)?",
        "/apr(il)?/": "ஏப்(.(ரல்)?)?",
        "/may/": "மே",
        "/jun(e)?/": "ஜூன்",
        "/jul(y)?/": "ஜூலை",
        "/aug(ust)?/": "ஆக(.(ஸ்ட்)?)?",
        "/sep(t(ember)?)?/": "செப்(.(டம்பர்)?)?",
        "/oct(ober)?/": "அக்(.(டோபர்)?)?",
        "/nov(ember)?/": "நவ(.(ம்பர்)?)?",
        "/dec(ember)?/": "டிச(.(ம்பர்)?)?",
        "/^su(n(day)?)?/": "^ஞ(ா(யிறு)?)?",
        "/^mo(n(day)?)?/": "^த(ி(ங்கள்)?)?",
        "/^tu(e(s(day)?)?)?/": "^ச(ெ(வ்வாய்)?)?",
        "/^we(d(nesday)?)?/": "^ப(ு(தன்)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^வ(ி(யாழன்)?)?",
        "/^fr(i(day)?)?/": "^வ(ெ(ள்ளி)?)?",
        "/^sa(t(urday)?)?/": "^சனி",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ta-IN";
