/* 
 * DateJS Culture String File
 * Country Code: sr-Cyrl-BA
 * Name: Serbian (Cyrillic) (Bosnia and Herzegovina)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["sr-Cyrl-BA"] = {
        "name": "sr-Cyrl-BA",
        "englishName": "Serbian (Cyrillic) (Bosnia and Herzegovina)",
        "nativeName": "српски (Босна и Херцеговина)",
        "Sunday": "недеља",
        "Monday": "понедељак",
        "Tuesday": "уторак",
        "Wednesday": "среда",
        "Thursday": "четвртак",
        "Friday": "петак",
        "Saturday": "субота",
        "Sun": "нед",
        "Mon": "пон",
        "Tue": "уто",
        "Wed": "сре",
        "Thu": "чет",
        "Fri": "пет",
        "Sat": "суб",
        "Su": "нед",
        "Mo": "пон",
        "Tu": "уто",
        "We": "сре",
        "Th": "чет",
        "Fr": "пет",
        "Sa": "суб",
        "S_Sun_Initial": "н",
        "M_Mon_Initial": "п",
        "T_Tue_Initial": "у",
        "W_Wed_Initial": "с",
        "T_Thu_Initial": "ч",
        "F_Fri_Initial": "п",
        "S_Sat_Initial": "с",
        "January": "јануар",
        "February": "фебруар",
        "March": "март",
        "April": "април",
        "May": "мај",
        "June": "јун",
        "July": "јул",
        "August": "август",
        "September": "септембар",
        "October": "октобар",
        "November": "новембар",
        "December": "децембар",
        "Jan_Abbr": "јан",
        "Feb_Abbr": "феб",
        "Mar_Abbr": "мар",
        "Apr_Abbr": "апр",
        "May_Abbr": "мај",
        "Jun_Abbr": "јун",
        "Jul_Abbr": "јул",
        "Aug_Abbr": "авг",
        "Sep_Abbr": "сеп",
        "Oct_Abbr": "окт",
        "Nov_Abbr": "нов",
        "Dec_Abbr": "дец",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "d. MMMM yyyy",
        "h:mm tt": "H:mm:ss",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d. MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "јан(уар)?",
        "/feb(ruary)?/": "феб(руар)?",
        "/mar(ch)?/": "мар(т)?",
        "/apr(il)?/": "апр(ил)?",
        "/may/": "мај",
        "/jun(e)?/": "јун",
        "/jul(y)?/": "јул",
        "/aug(ust)?/": "авг(уст)?",
        "/sep(t(ember)?)?/": "сеп(тембар)?",
        "/oct(ober)?/": "окт(обар)?",
        "/nov(ember)?/": "нов(ембар)?",
        "/dec(ember)?/": "дец(ембар)?",
        "/^su(n(day)?)?/": "^недеља",
        "/^mo(n(day)?)?/": "^понедељак",
        "/^tu(e(s(day)?)?)?/": "^уторак",
        "/^we(d(nesday)?)?/": "^среда",
        "/^th(u(r(s(day)?)?)?)?/": "^четвртак",
        "/^fr(i(day)?)?/": "^петак",
        "/^sa(t(urday)?)?/": "^субота",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "sr-Cyrl-BA";
