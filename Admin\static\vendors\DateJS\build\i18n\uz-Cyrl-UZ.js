/* 
 * DateJS Culture String File
 * Country Code: uz-Cyrl-UZ
 * Name: Uzbek (Cyrillic, Uzbekistan)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["uz-Cyrl-UZ"] = {
        "name": "uz-Cyrl-UZ",
        "englishName": "Uzbek (Cyrillic, Uzbekistan)",
        "nativeName": "Ўзбек (Ўзбекистон)",
        "Sunday": "якшанба",
        "Monday": "душанба",
        "Tuesday": "сешанба",
        "Wednesday": "чоршанба",
        "Thursday": "пайшанба",
        "Friday": "жума",
        "Saturday": "шанба",
        "Sun": "якш",
        "Mon": "дш",
        "Tue": "сш",
        "Wed": "чш",
        "Thu": "пш",
        "Fri": "ж",
        "Sat": "ш",
        "Su": "якш",
        "Mo": "дш",
        "Tu": "сш",
        "We": "чш",
        "Th": "пш",
        "Fr": "ж",
        "Sa": "ш",
        "S_Sun_Initial": "я",
        "M_Mon_Initial": "д",
        "T_Tue_Initial": "с",
        "W_Wed_Initial": "ч",
        "T_Thu_Initial": "п",
        "F_Fri_Initial": "ж",
        "S_Sat_Initial": "ш",
        "January": "Январ",
        "February": "Феврал",
        "March": "Март",
        "April": "Апрел",
        "May": "Май",
        "June": "Июн",
        "July": "Июл",
        "August": "Август",
        "September": "Сентябр",
        "October": "Октябр",
        "November": "Ноябр",
        "December": "Декабр",
        "Jan_Abbr": "Янв",
        "Feb_Abbr": "Фев",
        "Mar_Abbr": "Мар",
        "Apr_Abbr": "Апр",
        "May_Abbr": "Май",
        "Jun_Abbr": "Июн",
        "Jul_Abbr": "Июл",
        "Aug_Abbr": "Авг",
        "Sep_Abbr": "Сен",
        "Oct_Abbr": "Окт",
        "Nov_Abbr": "Ноя",
        "Dec_Abbr": "Дек",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "yyyy 'йил' d-MMMM",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy 'йил' d-MMMM HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d-MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "янв(ар)?",
        "/feb(ruary)?/": "фев(рал)?",
        "/mar(ch)?/": "мар(т)?",
        "/apr(il)?/": "апр(ел)?",
        "/may/": "май",
        "/jun(e)?/": "июн",
        "/jul(y)?/": "июл",
        "/aug(ust)?/": "авг(уст)?",
        "/sep(t(ember)?)?/": "сен(тябр)?",
        "/oct(ober)?/": "окт(ябр)?",
        "/nov(ember)?/": "ноя(бр)?",
        "/dec(ember)?/": "дек(абр)?",
        "/^su(n(day)?)?/": "^якшанба",
        "/^mo(n(day)?)?/": "^душанба",
        "/^tu(e(s(day)?)?)?/": "^сешанба",
        "/^we(d(nesday)?)?/": "^чоршанба",
        "/^th(u(r(s(day)?)?)?)?/": "^пайшанба",
        "/^fr(i(day)?)?/": "^жума",
        "/^sa(t(urday)?)?/": "^шанба",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "uz-Cyrl-UZ";
