/* 
 * DateJS Culture String File
 * Country Code: ka-GE
 * Name: Georgian (Georgia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["ka-GE"] = {
        "name": "ka-GE",
        "englishName": "Georgian (Georgia)",
        "nativeName": "ქართული (საქართველო)",
        "Sunday": "კვირა",
        "Monday": "ორშაბათი",
        "Tuesday": "სამშაბათი",
        "Wednesday": "ოთხშაბათი",
        "Thursday": "ხუთშაბათი",
        "Friday": "პარასკევი",
        "Saturday": "შაბათი",
        "Sun": "კვირა",
        "Mon": "ორშაბათი",
        "Tue": "სამშაბათი",
        "Wed": "ოთხშაბათი",
        "Thu": "ხუთშაბათი",
        "Fri": "პარასკევი",
        "Sat": "შაბათი",
        "Su": "კ",
        "Mo": "ო",
        "Tu": "ს",
        "We": "ო",
        "Th": "ხ",
        "Fr": "პ",
        "Sa": "შ",
        "S_Sun_Initial": "კ",
        "M_Mon_Initial": "ო",
        "T_Tue_Initial": "ს",
        "W_Wed_Initial": "ო",
        "T_Thu_Initial": "ხ",
        "F_Fri_Initial": "პ",
        "S_Sat_Initial": "შ",
        "January": "იანვარი",
        "February": "თებერვალი",
        "March": "მარტი",
        "April": "აპრილი",
        "May": "მაისი",
        "June": "ივნისი",
        "July": "ივლისი",
        "August": "აგვისტო",
        "September": "სექტემბერი",
        "October": "ოქტომბერი",
        "November": "ნოემბერი",
        "December": "დეკემბერი",
        "Jan_Abbr": "იან",
        "Feb_Abbr": "თებ",
        "Mar_Abbr": "მარ",
        "Apr_Abbr": "აპრ",
        "May_Abbr": "მაის",
        "Jun_Abbr": "ივნ",
        "Jul_Abbr": "ივლ",
        "Aug_Abbr": "აგვ",
        "Sep_Abbr": "სექ",
        "Oct_Abbr": "ოქტ",
        "Nov_Abbr": "ნოემ",
        "Dec_Abbr": "დეკ",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "yyyy 'წლის' dd MM, dddd",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "yyyy 'წლის' dd MM, dddd H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "იან(ვარი)?",
        "/feb(ruary)?/": "თებ(ერვალი)?",
        "/mar(ch)?/": "მარ(ტი)?",
        "/apr(il)?/": "აპრ(ილი)?",
        "/may/": "მაის(ი)?",
        "/jun(e)?/": "ივნ(ისი)?",
        "/jul(y)?/": "ივლ(ისი)?",
        "/aug(ust)?/": "აგვ(ისტო)?",
        "/sep(t(ember)?)?/": "სექ(ტემბერი)?",
        "/oct(ober)?/": "ოქტ(ომბერი)?",
        "/nov(ember)?/": "ნოემ(ბერი)?",
        "/dec(ember)?/": "დეკ(ემბერი)?",
        "/^su(n(day)?)?/": "^კ(1)?",
        "/^mo(n(day)?)?/": "^ო(1)?",
        "/^tu(e(s(day)?)?)?/": "^ს(1)?",
        "/^we(d(nesday)?)?/": "^ო(1)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ხ(1)?",
        "/^fr(i(day)?)?/": "^პ(1)?",
        "/^sa(t(urday)?)?/": "^შ(1)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "ka-GE";
