$progressbarVerticalWidth: $line-height-computed;
$progressbarFontSize: $font-size-small;

// bootstrap-progressbar global styles
// -----------------------------------

.progress {
  position: relative;
}

.progress .progress-bar {
  position: absolute;
  overflow: hidden;
  line-height: $line-height-computed;
}

.progress .progressbar-back-text {
  position: absolute;
  width: 100%;
  height: 100%;
  font-size: $progressbarFontSize;
  line-height: $line-height-computed;
  text-align: center;
}

.progress .progressbar-front-text {
  display: block;
  width: 100%;
  font-size: $progressbarFontSize;
  line-height: $line-height-computed;
  text-align: center;
}

// bootstrap-progressbar horizontal styles
// ---------------------------------------

.progress.right .progress-bar {
  right: 0;
}

.progress.right .progressbar-front-text {
  position: absolute;
  right: 0;
}

// bootstrap-progressbar vertical styles
// -------------------------------------

.progress.vertical {
  width: $progressbarVerticalWidth;
  height: 100%;
  float: left;
  margin-right: $progressbarVerticalWidth;
}

.progress.vertical.bottom {
  position: relative;
}

.progress.vertical.bottom .progressbar-front-text {
  position: absolute;
  bottom: 0;
}

.progress.vertical .progress-bar {
  width: 100%;
  height: 0;
  @include transition(height .6s ease);
}

.progress.vertical.bottom .progress-bar {
  position: absolute;
  bottom: 0;
}

// bootstrap-progressbar reverted low percentages styles
// -----------------------------------------------------

.progress-bar {
  &[aria-valuenow="1"],
  &[aria-valuenow="2"] {
    min-width: 0;
  }

  &[aria-valuenow="0"] {
    color: $progress-bar-color;
    min-width: 0;
    background-color: $progress-bar-bg;
    @include box-shadow(inset 0 -1px 0 rgba(0,0,0,.15));

    &.progress-bar-success {
      @include progress-bar-variant($progress-bar-success-bg);
    }

    &.progress-bar-info {
      @include progress-bar-variant($progress-bar-info-bg);
    }

    &.progress-bar-warning {
      @include progress-bar-variant($progress-bar-warning-bg);
    }

    &.progress-bar-danger {
      @include progress-bar-variant($progress-bar-danger-bg);
    }
  }
}
