/* 
 * DateJS Culture String File
 * Country Code: smn-FI
 * Name: <PERSON> (Inari) (Finland)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["smn-FI"] = {
        "name": "smn-FI",
        "englishName": "<PERSON> (Inari) (Finland)",
        "nativeName": "sämikielâ (Suomâ)",
        "Sunday": "pasepeivi",
        "Monday": "vuossargâ",
        "Tuesday": "majebargâ",
        "Wednesday": "koskokko",
        "Thursday": "tuorâstâh",
        "Friday": "vástuppeivi",
        "Saturday": "lávárdâh",
        "Sun": "pa",
        "Mon": "vu",
        "Tue": "ma",
        "Wed": "ko",
        "Thu": "tu",
        "Fri": "vá",
        "Sat": "lá",
        "Su": "pa",
        "Mo": "vu",
        "Tu": "ma",
        "We": "ko",
        "Th": "tu",
        "Fr": "vá",
        "Sa": "lá",
        "S_Sun_Initial": "p",
        "M_Mon_Initial": "v",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "k",
        "T_Thu_Initial": "t",
        "F_Fri_Initial": "v",
        "S_Sat_Initial": "l",
        "January": "uđđâivemáánu",
        "February": "kuovâmáánu",
        "March": "njuhčâmáánu",
        "April": "cuáŋuimáánu",
        "May": "vyesimáánu",
        "June": "kesimáánu",
        "July": "syeinimáánu",
        "August": "porgemáánu",
        "September": "čohčâmáánu",
        "October": "roovvâdmáánu",
        "November": "skammâmáánu",
        "December": "juovlâmáánu",
        "Jan_Abbr": "uđiv",
        "Feb_Abbr": "kuov",
        "Mar_Abbr": "njuh",
        "Apr_Abbr": "cuoŋ",
        "May_Abbr": "vyes",
        "Jun_Abbr": "kesi",
        "Jul_Abbr": "syei",
        "Aug_Abbr": "porg",
        "Sep_Abbr": "čoh",
        "Oct_Abbr": "roov",
        "Nov_Abbr": "ska",
        "Dec_Abbr": "juov",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "d.M.yyyy",
        "dddd, MMMM dd, yyyy": "MMMM d'. p. 'yyyy",
        "h:mm tt": "H:mm:ss",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "MMMM d'. p. 'yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "MMMM dd",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "uđđâivemáánu",
        "/feb(ruary)?/": "kuov(âmáánu)?",
        "/mar(ch)?/": "njuh(čâmáánu)?",
        "/apr(il)?/": "cuáŋuimáánu",
        "/may/": "vyes(imáánu)?",
        "/jun(e)?/": "kesi(máánu)?",
        "/jul(y)?/": "syei(nimáánu)?",
        "/aug(ust)?/": "porg(emáánu)?",
        "/sep(t(ember)?)?/": "čoh(čâmáánu)?",
        "/oct(ober)?/": "roov(vâdmáánu)?",
        "/nov(ember)?/": "ska(mmâmáánu)?",
        "/dec(ember)?/": "juov(lâmáánu)?",
        "/^su(n(day)?)?/": "^pasepeivi",
        "/^mo(n(day)?)?/": "^vuossargâ",
        "/^tu(e(s(day)?)?)?/": "^majebargâ",
        "/^we(d(nesday)?)?/": "^koskokko",
        "/^th(u(r(s(day)?)?)?)?/": "^tuorâstâh",
        "/^fr(i(day)?)?/": "^vástuppeivi",
        "/^sa(t(urday)?)?/": "^lávárdâh",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "smn-FI";
