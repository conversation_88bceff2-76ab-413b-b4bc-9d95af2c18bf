/* 
 * DateJS Culture String File
 * Country Code: tt-RU
 * Name: <PERSON><PERSON> (Russia)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["tt-RU"] = {
        "name": "tt-RU",
        "englishName": "<PERSON><PERSON> (Russia)",
        "nativeName": "Татар (Россия)",
        "Sunday": "Якшәмбе",
        "Monday": "Дүшәмбе",
        "Tuesday": "Сишәмбе",
        "Wednesday": "Чәршәмбе",
        "Thursday": "Пәнҗешәмбе",
        "Friday": "Җомга",
        "Saturday": "Шимбә",
        "Sun": "Якш",
        "Mon": "Дүш",
        "Tue": "Сиш",
        "Wed": "Чәрш",
        "Thu": "Пәнҗ",
        "Fri": "Җом",
        "Sat": "Шим",
        "Su": "Якш",
        "Mo": "Дүш",
        "Tu": "Сиш",
        "We": "Чәрш",
        "Th": "Пәнҗ",
        "Fr": "Җом",
        "Sa": "Шим",
        "S_Sun_Initial": "Я",
        "M_Mon_Initial": "Д",
        "T_Tue_Initial": "С",
        "W_Wed_Initial": "Ч",
        "T_Thu_Initial": "П",
        "F_Fri_Initial": "Җ",
        "S_Sat_Initial": "Ш",
        "January": "Гыйнварь",
        "February": "Февраль",
        "March": "Март",
        "April": "Апрель",
        "May": "Май",
        "June": "Июнь",
        "July": "Июль",
        "August": "Август",
        "September": "Сентябрь",
        "October": "Октябрь",
        "November": "Ноябрь",
        "December": "Декабрь",
        "Jan_Abbr": "Гыйнв",
        "Feb_Abbr": "Фев",
        "Mar_Abbr": "Мар",
        "Apr_Abbr": "Апр",
        "May_Abbr": "Май",
        "Jun_Abbr": "Июн",
        "Jul_Abbr": "Июл",
        "Aug_Abbr": "Авг",
        "Sep_Abbr": "Сен",
        "Oct_Abbr": "Окт",
        "Nov_Abbr": "Ноя",
        "Dec_Abbr": "Дек",
        "AM": "",
        "PM": "",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd.MM.yyyy",
        "dddd, MMMM dd, yyyy": "d MMMM yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "d MMMM yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "d MMMM",
        "MMMM, yyyy": "MMMM yyyy",
        "/jan(uary)?/": "гыйнв(арь)?",
        "/feb(ruary)?/": "фев(раль)?",
        "/mar(ch)?/": "мар(т)?",
        "/apr(il)?/": "апр(ель)?",
        "/may/": "май",
        "/jun(e)?/": "июн(ь)?",
        "/jul(y)?/": "июл(ь)?",
        "/aug(ust)?/": "авг(уст)?",
        "/sep(t(ember)?)?/": "сен(тябрь)?",
        "/oct(ober)?/": "окт(ябрь)?",
        "/nov(ember)?/": "ноя(брь)?",
        "/dec(ember)?/": "дек(абрь)?",
        "/^su(n(day)?)?/": "^якшәмбе",
        "/^mo(n(day)?)?/": "^дүшәмбе",
        "/^tu(e(s(day)?)?)?/": "^сишәмбе",
        "/^we(d(nesday)?)?/": "^чәршәмбе",
        "/^th(u(r(s(day)?)?)?)?/": "^пәнҗешәмбе",
        "/^fr(i(day)?)?/": "^җомга",
        "/^sa(t(urday)?)?/": "^шимбә",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "tt-RU";
