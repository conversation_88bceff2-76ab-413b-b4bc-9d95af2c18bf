/* 
 * DateJS Culture String File
 * Country Code: syr-SY
 * Name: Syriac (Syria)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["syr-SY"] = {
        "name": "syr-SY",
        "englishName": "Syriac (Syria)",
        "nativeName": "ܣܘܪܝܝܐ (سوريا)",
        "Sunday": "ܚܕ ܒܫܒܐ",
        "Monday": "ܬܪܝܢ ܒܫܒܐ",
        "Tuesday": "ܬܠܬܐ ܒܫܒܐ",
        "Wednesday": "ܐܪܒܥܐ ܒܫܒܐ",
        "Thursday": "ܚܡܫܐ ܒܫܒܐ",
        "Friday": "ܥܪܘܒܬܐ",
        "Saturday": "ܫܒܬܐ",
        "Sun": "܏ܐ ܏ܒܫ",
        "Mon": "܏ܒ ܏ܒܫ",
        "Tue": "܏ܓ ܏ܒܫ",
        "Wed": "܏ܕ ܏ܒܫ",
        "Thu": "܏ܗ ܏ܒܫ",
        "Fri": "܏ܥܪܘܒ",
        "Sat": "܏ܫܒ",
        "Su": "܏",
        "Mo": "܏",
        "Tu": "܏",
        "We": "܏",
        "Th": "܏",
        "Fr": "܏",
        "Sa": "܏",
        "S_Sun_Initial": "܏",
        "M_Mon_Initial": "܏",
        "T_Tue_Initial": "܏",
        "W_Wed_Initial": "܏",
        "T_Thu_Initial": "܏",
        "F_Fri_Initial": "܏",
        "S_Sat_Initial": "܏",
        "January": "ܟܢܘܢ ܐܚܪܝ",
        "February": "ܫܒܛ",
        "March": "ܐܕܪ",
        "April": "ܢܝܣܢ",
        "May": "ܐܝܪ",
        "June": "ܚܙܝܪܢ",
        "July": "ܬܡܘܙ",
        "August": "ܐܒ",
        "September": "ܐܝܠܘܠ",
        "October": "ܬܫܪܝ ܩܕܝܡ",
        "November": "ܬܫܪܝ ܐܚܪܝ",
        "December": "ܟܢܘܢ ܩܕܝܡ",
        "Jan_Abbr": "܏ܟܢ ܏ܒ",
        "Feb_Abbr": "ܫܒܛ",
        "Mar_Abbr": "ܐܕܪ",
        "Apr_Abbr": "ܢܝܣܢ",
        "May_Abbr": "ܐܝܪ",
        "Jun_Abbr": "ܚܙܝܪܢ",
        "Jul_Abbr": "ܬܡܘܙ",
        "Aug_Abbr": "ܐܒ",
        "Sep_Abbr": "ܐܝܠܘܠ",
        "Oct_Abbr": "܏ܬܫ ܏ܐ",
        "Nov_Abbr": "܏ܬܫ ܏ܒ",
        "Dec_Abbr": "܏ܟܢ ܏ܐ",
        "AM": "ܩ.ܛ",
        "PM": "ܒ.ܛ",
        "firstDayOfWeek": 6,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM, yyyy",
        "h:mm tt": "hh:mm tt",
        "h:mm:ss tt": "hh:mm:ss tt",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM, yyyy hh:mm:ss tt",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "ܟܢܘܢ ܐܚܪܝ",
        "/feb(ruary)?/": "ܫܒܛ",
        "/mar(ch)?/": "ܐܕܪ",
        "/apr(il)?/": "ܢܝܣܢ",
        "/may/": "ܐܝܪ",
        "/jun(e)?/": "ܚܙܝܪܢ",
        "/jul(y)?/": "ܬܡܘܙ",
        "/aug(ust)?/": "ܐܒ",
        "/sep(t(ember)?)?/": "ܐܝܠܘܠ",
        "/oct(ober)?/": "ܬܫܪܝ ܩܕܝܡ",
        "/nov(ember)?/": "ܬܫܪܝ ܐܚܪܝ",
        "/dec(ember)?/": "ܟܢܘܢ ܩܕܝܡ",
        "/^su(n(day)?)?/": "^܏(ܐ ܏ܒܫ(ܐ)?)?",
        "/^mo(n(day)?)?/": "^܏(ܒ ܏ܒܫ(ܫܒܐ)?)?",
        "/^tu(e(s(day)?)?)?/": "^܏(ܓ ܏ܒܫ(ܫܒܐ)?)?",
        "/^we(d(nesday)?)?/": "^܏(ܕ ܏ܒܫ(ܒܫܒܐ)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^܏(ܗ ܏ܒܫ(ܫܒܐ)?)?",
        "/^fr(i(day)?)?/": "^܏(ܥܪܘܒ(ܐ)?)?",
        "/^sa(t(urday)?)?/": "^܏(ܫܒ(ܐ)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "syr-SY";
