/* 
 * DateJS Culture String File
 * Country Code: hi-IN
 * Name: Hindi (India)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["hi-IN"] = {
        "name": "hi-IN",
        "englishName": "Hindi (India)",
        "nativeName": "हिंदी (भारत)",
        "Sunday": "रविवार",
        "Monday": "सोमवार",
        "Tuesday": "मंगलवार",
        "Wednesday": "बुधवार",
        "Thursday": "गुरुवार",
        "Friday": "शुक्रवार",
        "Saturday": "शनिवार",
        "Sun": "रवि.",
        "Mon": "सोम.",
        "Tue": "मंगल.",
        "Wed": "बुध.",
        "Thu": "गुरु.",
        "Fri": "शुक्र.",
        "Sat": "शनि.",
        "Su": "र",
        "Mo": "स",
        "Tu": "म",
        "We": "ब",
        "Th": "ग",
        "Fr": "श",
        "Sa": "श",
        "S_Sun_Initial": "र",
        "M_Mon_Initial": "स",
        "T_Tue_Initial": "म",
        "W_Wed_Initial": "ब",
        "T_Thu_Initial": "ग",
        "F_Fri_Initial": "श",
        "S_Sat_Initial": "श",
        "January": "जनवरी",
        "February": "फरवरी",
        "March": "मार्च",
        "April": "अप्रैल",
        "May": "मई",
        "June": "जून",
        "July": "जुलाई",
        "August": "अगस्त",
        "September": "सितम्बर",
        "October": "अक्तूबर",
        "November": "नवम्बर",
        "December": "दिसम्बर",
        "Jan_Abbr": "जनवरी",
        "Feb_Abbr": "फरवरी",
        "Mar_Abbr": "मार्च",
        "Apr_Abbr": "अप्रैल",
        "May_Abbr": "मई",
        "Jun_Abbr": "जून",
        "Jul_Abbr": "जुलाई",
        "Aug_Abbr": "अगस्त",
        "Sep_Abbr": "सितम्बर",
        "Oct_Abbr": "अक्तूबर",
        "Nov_Abbr": "नवम्बर",
        "Dec_Abbr": "दिसम्बर",
        "AM": "पूर्वाह्न",
        "PM": "अपराह्न",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd-MM-yyyy",
        "dddd, MMMM dd, yyyy": "dd MMMM yyyy",
        "h:mm tt": "HH:mm",
        "h:mm:ss tt": "HH:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dd MMMM yyyy HH:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM, yyyy",
        "/jan(uary)?/": "जनवरी",
        "/feb(ruary)?/": "फरवरी",
        "/mar(ch)?/": "मार्च",
        "/apr(il)?/": "अप्रैल",
        "/may/": "मई",
        "/jun(e)?/": "जून",
        "/jul(y)?/": "जुलाई",
        "/aug(ust)?/": "अगस्त",
        "/sep(t(ember)?)?/": "सितम्बर",
        "/oct(ober)?/": "अक्तूबर",
        "/nov(ember)?/": "नवम्बर",
        "/dec(ember)?/": "दिसम्बर",
        "/^su(n(day)?)?/": "^र(वि(.(वार)?)?)?",
        "/^mo(n(day)?)?/": "^स(ोम(.(वार)?)?)?",
        "/^tu(e(s(day)?)?)?/": "^म(ंगल(.(वार)?)?)?",
        "/^we(d(nesday)?)?/": "^ब(ुध(.(वार)?)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^ग(ुरु(.(वार)?)?)?",
        "/^fr(i(day)?)?/": "^श(ुक्र(.(वार)?)?)?",
        "/^sa(t(urday)?)?/": "^श(नि(.(वार)?)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "hi-IN";
