/* 
 * DateJS Culture String File
 * Country Code: gl-ES
 * Name: Galician (Galician)
 * Format: "key" : "value"
 * Key is the en-US term, Value is the Key in the current language.
 */
Date.CultureStrings = Date.CultureStrings || {};
Date.CultureStrings["gl-ES"] = {
        "name": "gl-ES",
        "englishName": "Galician (Galician)",
        "nativeName": "galego (galego)",
        "Sunday": "domingo",
        "Monday": "luns",
        "Tuesday": "martes",
        "Wednesday": "mércores",
        "Thursday": "xoves",
        "Friday": "venres",
        "Saturday": "sábado",
        "Sun": "dom",
        "Mon": "luns",
        "Tue": "mar",
        "Wed": "mér",
        "Thu": "xov",
        "Fri": "ven",
        "Sat": "sab",
        "Su": "do",
        "Mo": "lu",
        "Tu": "ma",
        "We": "mé",
        "Th": "xo",
        "Fr": "ve",
        "Sa": "sa",
        "S_Sun_Initial": "d",
        "M_Mon_Initial": "l",
        "T_Tue_Initial": "m",
        "W_Wed_Initial": "m",
        "T_Thu_Initial": "x",
        "F_Fri_Initial": "v",
        "S_Sat_Initial": "s",
        "January": "xaneiro",
        "February": "febreiro",
        "March": "marzo",
        "April": "abril",
        "May": "maio",
        "June": "xuño",
        "July": "xullo",
        "August": "agosto",
        "September": "setembro",
        "October": "outubro",
        "November": "novembro",
        "December": "decembro",
        "Jan_Abbr": "xan",
        "Feb_Abbr": "feb",
        "Mar_Abbr": "mar",
        "Apr_Abbr": "abr",
        "May_Abbr": "maio",
        "Jun_Abbr": "xuñ",
        "Jul_Abbr": "xull",
        "Aug_Abbr": "ago",
        "Sep_Abbr": "set",
        "Oct_Abbr": "out",
        "Nov_Abbr": "nov",
        "Dec_Abbr": "dec",
        "AM": "a.m.",
        "PM": "p.m.",
        "firstDayOfWeek": 1,
        "twoDigitYearMax": 2029,
        "mdy": "dmy",
        "M/d/yyyy": "dd/MM/yy",
        "dddd, MMMM dd, yyyy": "dddd, dd' de 'MMMM' de 'yyyy",
        "h:mm tt": "H:mm",
        "h:mm:ss tt": "H:mm:ss",
        "dddd, MMMM dd, yyyy h:mm:ss tt": "dddd, dd' de 'MMMM' de 'yyyy H:mm:ss",
        "yyyy-MM-ddTHH:mm:ss": "yyyy-MM-ddTHH:mm:ss",
        "yyyy-MM-dd HH:mm:ssZ": "yyyy-MM-dd HH:mm:ssZ",
        "ddd, dd MMM yyyy HH:mm:ss": "ddd, dd MMM yyyy HH:mm:ss",
        "MMMM dd": "dd MMMM",
        "MMMM, yyyy": "MMMM' de 'yyyy",
        "/jan(uary)?/": "xan(eiro)?",
        "/feb(ruary)?/": "feb(reiro)?",
        "/mar(ch)?/": "mar(zo)?",
        "/apr(il)?/": "abr(il)?",
        "/may/": "maio",
        "/jun(e)?/": "xuñ(o)?",
        "/jul(y)?/": "xull(o)?",
        "/aug(ust)?/": "ago(sto)?",
        "/sep(t(ember)?)?/": "set(embro)?",
        "/oct(ober)?/": "out(ubro)?",
        "/nov(ember)?/": "nov(embro)?",
        "/dec(ember)?/": "dec(embro)?",
        "/^su(n(day)?)?/": "^do(m(ingo)?)?",
        "/^mo(n(day)?)?/": "^lu(1)?",
        "/^tu(e(s(day)?)?)?/": "^ma(r(tes)?)?",
        "/^we(d(nesday)?)?/": "^mé(r(cores)?)?",
        "/^th(u(r(s(day)?)?)?)?/": "^xo(v(es)?)?",
        "/^fr(i(day)?)?/": "^ve(n(res)?)?",
        "/^sa(t(urday)?)?/": "^sa(b(ado)?)?",
        "/^next/": "^next",
        "/^last|past|prev(ious)?/": "^last|past|prev(ious)?",
        "/^(\\+|aft(er)?|from|hence)/": "^(\\+|aft(er)?|from|hence)",
        "/^(\\-|bef(ore)?|ago)/": "^(\\-|bef(ore)?|ago)",
        "/^yes(terday)?/": "^yes(terday)?",
        "/^t(od(ay)?)?/": "^t(od(ay)?)?",
        "/^tom(orrow)?/": "^tom(orrow)?",
        "/^n(ow)?/": "^n(ow)?",
        "/^ms|milli(second)?s?/": "^ms|milli(second)?s?",
        "/^sec(ond)?s?/": "^sec(ond)?s?",
        "/^mn|min(ute)?s?/": "^mn|min(ute)?s?",
        "/^h(our)?s?/": "^h(our)?s?",
        "/^w(eek)?s?/": "^w(eek)?s?",
        "/^m(onth)?s?/": "^m(onth)?s?",
        "/^d(ay)?s?/": "^d(ay)?s?",
        "/^y(ear)?s?/": "^y(ear)?s?",
        "/^(a|p)/": "^(a|p)",
        "/^(a\\.?m?\\.?|p\\.?m?\\.?)/": "^(a\\.?m?\\.?|p\\.?m?\\.?)",
        "/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)/": "^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt|utc)",
        "/^\\s*(st|nd|rd|th)/": "^\\s*(st|nd|rd|th)",
        "/^\\s*(\\:|a(?!u|p)|p)/": "^\\s*(\\:|a(?!u|p)|p)",
        "LINT": "LINT",
        "TOT": "TOT",
        "CHAST": "CHAST",
        "NZST": "NZST",
        "NFT": "NFT",
        "SBT": "SBT",
        "AEST": "AEST",
        "ACST": "ACST",
        "JST": "JST",
        "CWST": "CWST",
        "CT": "CT",
        "ICT": "ICT",
        "MMT": "MMT",
        "BIOT": "BST",
        "NPT": "NPT",
        "IST": "IST",
        "PKT": "PKT",
        "AFT": "AFT",
        "MSK": "MSK",
        "IRST": "IRST",
        "FET": "FET",
        "EET": "EET",
        "CET": "CET",
        "UTC": "UTC",
        "GMT": "GMT",
        "CVT": "CVT",
        "GST": "GST",
        "BRT": "BRT",
        "NST": "NST",
        "AST": "AST",
        "EST": "EST",
        "CST": "CST",
        "MST": "MST",
        "PST": "PST",
        "AKST": "AKST",
        "MIT": "MIT",
        "HST": "HST",
        "SST": "SST",
        "BIT": "BIT",
        "CHADT": "CHADT",
        "NZDT": "NZDT",
        "AEDT": "AEDT",
        "ACDT": "ACDT",
        "AZST": "AZST",
        "IRDT": "IRDT",
        "EEST": "EEST",
        "CEST": "CEST",
        "BST": "BST",
        "PMDT": "PMDT",
        "ADT": "ADT",
        "NDT": "NDT",
        "EDT": "EDT",
        "CDT": "CDT",
        "MDT": "MDT",
        "PDT": "PDT",
        "AKDT": "AKDT",
        "HADT": "HADT"
};
Date.CultureStrings.lang = "gl-ES";
